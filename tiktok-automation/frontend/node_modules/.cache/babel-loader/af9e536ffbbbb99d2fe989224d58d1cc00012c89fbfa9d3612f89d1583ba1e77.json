{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Layout/Sidebar.jsx\",\n  _s = $RefreshSig$();\n/**\n * Sidebar Navigation Component\n */\n\nimport React, { useState } from 'react';\nimport { NavLink, useLocation } from 'react-router-dom';\nimport { FiHome, FiUsers, FiGlobe, FiPlay, FiSettings, FiBarChart2, FiTarget, FiChevronLeft, FiChevronRight } from 'react-icons/fi';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Sidebar = ({\n  isCollapsed,\n  onToggle\n}) => {\n  _s();\n  const location = useLocation();\n  const menuItems = [{\n    path: '/',\n    icon: FiHome,\n    label: 'Dashboard',\n    description: 'Overview and statistics'\n  }, {\n    path: '/profiles',\n    icon: FiUsers,\n    label: 'Profiles',\n    description: 'Browser profiles management'\n  }, {\n    path: '/proxies',\n    icon: FiGlobe,\n    label: 'Proxies',\n    description: 'Proxy configuration'\n  }, {\n    path: '/accounts',\n    icon: FiTarget,\n    label: 'Accounts',\n    description: 'TikTok accounts'\n  }, {\n    path: '/tasks',\n    icon: FiPlay,\n    label: 'Tasks',\n    description: 'Automation tasks'\n  }, {\n    path: '/analytics',\n    icon: FiBarChart3,\n    label: 'Analytics',\n    description: 'Performance metrics'\n  }, {\n    path: '/settings',\n    icon: FiSettings,\n    label: 'Settings',\n    description: 'Application settings'\n  }];\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    className: `bg-gray-900 text-white h-screen flex flex-col transition-all duration-300 ${isCollapsed ? 'w-16' : 'w-64'}`,\n    initial: false,\n    animate: {\n      width: isCollapsed ? 64 : 256\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 border-b border-gray-700\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: !isCollapsed && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -20\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            exit: {\n              opacity: 0,\n              x: -20\n            },\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-bold text-sm\",\n                children: \"TA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-lg font-bold\",\n                children: \"TikTok Auto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-400\",\n                children: \"v1.0.0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 92,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onToggle,\n          className: \"p-1 rounded-lg hover:bg-gray-700 transition-colors\",\n          children: isCollapsed ? /*#__PURE__*/_jsxDEV(FiChevronRight, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(FiChevronLeft, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"flex-1 p-4 space-y-2\",\n      children: menuItems.map(item => {\n        const Icon = item.icon;\n        const isActive = location.pathname === item.path;\n        return /*#__PURE__*/_jsxDEV(NavLink, {\n          to: item.path,\n          className: `flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 group relative ${isActive ? 'bg-blue-600 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'}`,\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            className: `w-5 h-5 ${isActive ? 'text-white' : 'text-gray-400'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n            children: !isCollapsed && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                x: -10\n              },\n              animate: {\n                opacity: 1,\n                x: 0\n              },\n              exit: {\n                opacity: 0,\n                x: -10\n              },\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"font-medium\",\n                children: item.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-400 group-hover:text-gray-300\",\n                children: item.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 15\n          }, this), isCollapsed && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50\",\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 17\n          }, this), isActive && /*#__PURE__*/_jsxDEV(motion.div, {\n            layoutId: \"activeIndicator\",\n            className: \"absolute right-0 w-1 h-8 bg-white rounded-l-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 17\n          }, this)]\n        }, item.path, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 border-t border-gray-700\",\n      children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: !isCollapsed && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          exit: {\n            opacity: 0,\n            y: 20\n          },\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-400 mb-2\",\n            children: \"System Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs text-green-400\",\n              children: \"Online\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 5\n  }, this);\n};\n_s(Sidebar, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "useState", "NavLink", "useLocation", "FiHome", "FiUsers", "FiGlobe", "FiPlay", "FiSettings", "FiBarChart2", "<PERSON><PERSON><PERSON><PERSON>", "FiChevronLeft", "FiChevronRight", "motion", "AnimatePresence", "jsxDEV", "_jsxDEV", "Sidebar", "isCollapsed", "onToggle", "_s", "location", "menuItems", "path", "icon", "label", "description", "FiBarChart3", "div", "className", "initial", "animate", "width", "children", "opacity", "x", "exit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "item", "Icon", "isActive", "pathname", "to", "layoutId", "y", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Layout/Sidebar.jsx"], "sourcesContent": ["/**\n * Sidebar Navigation Component\n */\n\nimport React, { useState } from 'react';\nimport { NavLink, useLocation } from 'react-router-dom';\nimport {\n  FiHome,\n  FiUsers,\n  FiGlobe,\n  FiPlay,\n  FiSettings,\n  FiBarChart2,\n  FiTarget,\n  FiChevronLeft,\n  FiChevronRight\n} from 'react-icons/fi';\nimport { motion, AnimatePresence } from 'framer-motion';\n\nconst Sidebar = ({ isCollapsed, onToggle }) => {\n  const location = useLocation();\n\n  const menuItems = [\n    {\n      path: '/',\n      icon: FiHome,\n      label: 'Dashboard',\n      description: 'Overview and statistics'\n    },\n    {\n      path: '/profiles',\n      icon: FiUsers,\n      label: 'Profiles',\n      description: 'Browser profiles management'\n    },\n    {\n      path: '/proxies',\n      icon: FiGlobe,\n      label: 'Proxies',\n      description: 'Proxy configuration'\n    },\n    {\n      path: '/accounts',\n      icon: FiTarget,\n      label: 'Accounts',\n      description: 'TikTok accounts'\n    },\n    {\n      path: '/tasks',\n      icon: FiPlay,\n      label: 'Tasks',\n      description: 'Automation tasks'\n    },\n    {\n      path: '/analytics',\n      icon: FiBarChart3,\n      label: 'Analytics',\n      description: 'Performance metrics'\n    },\n    {\n      path: '/settings',\n      icon: FiSettings,\n      label: 'Settings',\n      description: 'Application settings'\n    }\n  ];\n\n  return (\n    <motion.div\n      className={`bg-gray-900 text-white h-screen flex flex-col transition-all duration-300 ${\n        isCollapsed ? 'w-16' : 'w-64'\n      }`}\n      initial={false}\n      animate={{ width: isCollapsed ? 64 : 256 }}\n    >\n      {/* Header */}\n      <div className=\"p-4 border-b border-gray-700\">\n        <div className=\"flex items-center justify-between\">\n          <AnimatePresence>\n            {!isCollapsed && (\n              <motion.div\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                exit={{ opacity: 0, x: -20 }}\n                className=\"flex items-center space-x-3\"\n              >\n                <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">TA</span>\n                </div>\n                <div>\n                  <h1 className=\"text-lg font-bold\">TikTok Auto</h1>\n                  <p className=\"text-xs text-gray-400\">v1.0.0</p>\n                </div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n          \n          <button\n            onClick={onToggle}\n            className=\"p-1 rounded-lg hover:bg-gray-700 transition-colors\"\n          >\n            {isCollapsed ? (\n              <FiChevronRight className=\"w-5 h-5\" />\n            ) : (\n              <FiChevronLeft className=\"w-5 h-5\" />\n            )}\n          </button>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 p-4 space-y-2\">\n        {menuItems.map((item) => {\n          const Icon = item.icon;\n          const isActive = location.pathname === item.path;\n\n          return (\n            <NavLink\n              key={item.path}\n              to={item.path}\n              className={`flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 group relative ${\n                isActive\n                  ? 'bg-blue-600 text-white'\n                  : 'text-gray-300 hover:bg-gray-700 hover:text-white'\n              }`}\n            >\n              <Icon className={`w-5 h-5 ${isActive ? 'text-white' : 'text-gray-400'}`} />\n              \n              <AnimatePresence>\n                {!isCollapsed && (\n                  <motion.div\n                    initial={{ opacity: 0, x: -10 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    exit={{ opacity: 0, x: -10 }}\n                    className=\"flex-1\"\n                  >\n                    <div className=\"font-medium\">{item.label}</div>\n                    <div className=\"text-xs text-gray-400 group-hover:text-gray-300\">\n                      {item.description}\n                    </div>\n                  </motion.div>\n                )}\n              </AnimatePresence>\n\n              {/* Tooltip for collapsed state */}\n              {isCollapsed && (\n                <div className=\"absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50\">\n                  {item.label}\n                </div>\n              )}\n\n              {/* Active indicator */}\n              {isActive && (\n                <motion.div\n                  layoutId=\"activeIndicator\"\n                  className=\"absolute right-0 w-1 h-8 bg-white rounded-l-full\"\n                />\n              )}\n            </NavLink>\n          );\n        })}\n      </nav>\n\n      {/* Footer */}\n      <div className=\"p-4 border-t border-gray-700\">\n        <AnimatePresence>\n          {!isCollapsed && (\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: 20 }}\n              className=\"text-center\"\n            >\n              <div className=\"text-xs text-gray-400 mb-2\">\n                System Status\n              </div>\n              <div className=\"flex items-center justify-center space-x-2\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                <span className=\"text-xs text-green-400\">Online</span>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,EAAEC,WAAW,QAAQ,kBAAkB;AACvD,SACEC,MAAM,EACNC,OAAO,EACPC,OAAO,EACPC,MAAM,EACNC,UAAU,EACVC,WAAW,EACXC,QAAQ,EACRC,aAAa,EACbC,cAAc,QACT,gBAAgB;AACvB,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,OAAO,GAAGA,CAAC;EAAEC,WAAW;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC7C,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAE9B,MAAMmB,SAAS,GAAG,CAChB;IACEC,IAAI,EAAE,GAAG;IACTC,IAAI,EAAEpB,MAAM;IACZqB,KAAK,EAAE,WAAW;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEH,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAEnB,OAAO;IACboB,KAAK,EAAE,UAAU;IACjBC,WAAW,EAAE;EACf,CAAC,EACD;IACEH,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAElB,OAAO;IACbmB,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE;EACf,CAAC,EACD;IACEH,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAEd,QAAQ;IACde,KAAK,EAAE,UAAU;IACjBC,WAAW,EAAE;EACf,CAAC,EACD;IACEH,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAEjB,MAAM;IACZkB,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE;EACf,CAAC,EACD;IACEH,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAEG,WAAW;IACjBF,KAAK,EAAE,WAAW;IAClBC,WAAW,EAAE;EACf,CAAC,EACD;IACEH,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAEhB,UAAU;IAChBiB,KAAK,EAAE,UAAU;IACjBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,oBACEV,OAAA,CAACH,MAAM,CAACe,GAAG;IACTC,SAAS,EAAE,6EACTX,WAAW,GAAG,MAAM,GAAG,MAAM,EAC5B;IACHY,OAAO,EAAE,KAAM;IACfC,OAAO,EAAE;MAAEC,KAAK,EAAEd,WAAW,GAAG,EAAE,GAAG;IAAI,CAAE;IAAAe,QAAA,gBAG3CjB,OAAA;MAAKa,SAAS,EAAC,8BAA8B;MAAAI,QAAA,eAC3CjB,OAAA;QAAKa,SAAS,EAAC,mCAAmC;QAAAI,QAAA,gBAChDjB,OAAA,CAACF,eAAe;UAAAmB,QAAA,EACb,CAACf,WAAW,iBACXF,OAAA,CAACH,MAAM,CAACe,GAAG;YACTE,OAAO,EAAE;cAAEI,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCJ,OAAO,EAAE;cAAEG,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BC,IAAI,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE,CAAC;YAAG,CAAE;YAC7BN,SAAS,EAAC,6BAA6B;YAAAI,QAAA,gBAEvCjB,OAAA;cAAKa,SAAS,EAAC,kGAAkG;cAAAI,QAAA,eAC/GjB,OAAA;gBAAMa,SAAS,EAAC,8BAA8B;gBAAAI,QAAA,EAAC;cAAE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACNxB,OAAA;cAAAiB,QAAA,gBACEjB,OAAA;gBAAIa,SAAS,EAAC,mBAAmB;gBAAAI,QAAA,EAAC;cAAW;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClDxB,OAAA;gBAAGa,SAAS,EAAC,uBAAuB;gBAAAI,QAAA,EAAC;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC,eAElBxB,OAAA;UACEyB,OAAO,EAAEtB,QAAS;UAClBU,SAAS,EAAC,oDAAoD;UAAAI,QAAA,EAE7Df,WAAW,gBACVF,OAAA,CAACJ,cAAc;YAACiB,SAAS,EAAC;UAAS;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEtCxB,OAAA,CAACL,aAAa;YAACkB,SAAS,EAAC;UAAS;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACrC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxB,OAAA;MAAKa,SAAS,EAAC,sBAAsB;MAAAI,QAAA,EAClCX,SAAS,CAACoB,GAAG,CAAEC,IAAI,IAAK;QACvB,MAAMC,IAAI,GAAGD,IAAI,CAACnB,IAAI;QACtB,MAAMqB,QAAQ,GAAGxB,QAAQ,CAACyB,QAAQ,KAAKH,IAAI,CAACpB,IAAI;QAEhD,oBACEP,OAAA,CAACd,OAAO;UAEN6C,EAAE,EAAEJ,IAAI,CAACpB,IAAK;UACdM,SAAS,EAAE,yFACTgB,QAAQ,GACJ,wBAAwB,GACxB,kDAAkD,EACrD;UAAAZ,QAAA,gBAEHjB,OAAA,CAAC4B,IAAI;YAACf,SAAS,EAAE,WAAWgB,QAAQ,GAAG,YAAY,GAAG,eAAe;UAAG;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAE3ExB,OAAA,CAACF,eAAe;YAAAmB,QAAA,EACb,CAACf,WAAW,iBACXF,OAAA,CAACH,MAAM,CAACe,GAAG;cACTE,OAAO,EAAE;gBAAEI,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE,CAAC;cAAG,CAAE;cAChCJ,OAAO,EAAE;gBAAEG,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BC,IAAI,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE,CAAC;cAAG,CAAE;cAC7BN,SAAS,EAAC,QAAQ;cAAAI,QAAA,gBAElBjB,OAAA;gBAAKa,SAAS,EAAC,aAAa;gBAAAI,QAAA,EAAEU,IAAI,CAAClB;cAAK;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/CxB,OAAA;gBAAKa,SAAS,EAAC,iDAAiD;gBAAAI,QAAA,EAC7DU,IAAI,CAACjB;cAAW;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UACb;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACc,CAAC,EAGjBtB,WAAW,iBACVF,OAAA;YAAKa,SAAS,EAAC,0LAA0L;YAAAI,QAAA,EACtMU,IAAI,CAAClB;UAAK;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CACN,EAGAK,QAAQ,iBACP7B,OAAA,CAACH,MAAM,CAACe,GAAG;YACToB,QAAQ,EAAC,iBAAiB;YAC1BnB,SAAS,EAAC;UAAkD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CACF;QAAA,GAvCIG,IAAI,CAACpB,IAAI;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAwCP,CAAC;MAEd,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNxB,OAAA;MAAKa,SAAS,EAAC,8BAA8B;MAAAI,QAAA,eAC3CjB,OAAA,CAACF,eAAe;QAAAmB,QAAA,EACb,CAACf,WAAW,iBACXF,OAAA,CAACH,MAAM,CAACe,GAAG;UACTE,OAAO,EAAE;YAAEI,OAAO,EAAE,CAAC;YAAEe,CAAC,EAAE;UAAG,CAAE;UAC/BlB,OAAO,EAAE;YAAEG,OAAO,EAAE,CAAC;YAAEe,CAAC,EAAE;UAAE,CAAE;UAC9Bb,IAAI,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEe,CAAC,EAAE;UAAG,CAAE;UAC5BpB,SAAS,EAAC,aAAa;UAAAI,QAAA,gBAEvBjB,OAAA;YAAKa,SAAS,EAAC,4BAA4B;YAAAI,QAAA,EAAC;UAE5C;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNxB,OAAA;YAAKa,SAAS,EAAC,4CAA4C;YAAAI,QAAA,gBACzDjB,OAAA;cAAKa,SAAS,EAAC;YAAiD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvExB,OAAA;cAAMa,SAAS,EAAC,wBAAwB;cAAAI,QAAA,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACc;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEjB,CAAC;AAACpB,EAAA,CAvKIH,OAAO;EAAA,QACMd,WAAW;AAAA;AAAA+C,EAAA,GADxBjC,OAAO;AAyKb,eAAeA,OAAO;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}