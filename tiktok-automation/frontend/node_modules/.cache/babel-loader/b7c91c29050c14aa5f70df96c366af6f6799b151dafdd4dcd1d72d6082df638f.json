{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Dashboard/ProfileForm.jsx\",\n  _s = $RefreshSig$();\n/**\n * ProfileForm Component - Form for creating new browser profiles\n */\n\nimport React, { useState } from 'react';\nimport { FiX, FiCheck, FiGlobe } from 'react-icons/fi';\nimport profileService from '../../services/profileService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProfileForm = ({\n  isOpen,\n  onClose,\n  onSubmit\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    proxyType: 'no-proxy',\n    ipChecker: 'IP2Location',\n    host: '',\n    port: '',\n    username: '',\n    password: '',\n    profileName: ''\n  });\n  const [proxyStatus, setProxyStatus] = useState(null);\n  const [isChecking, setIsChecking] = useState(false);\n  const proxyTypes = [{\n    value: 'no-proxy',\n    label: 'No proxy (local network)'\n  }, {\n    value: 'http',\n    label: 'HTTP'\n  }, {\n    value: 'https',\n    label: 'HTTPS'\n  }, {\n    value: 'socks5',\n    label: 'SOCKS5'\n  }, {\n    value: 'ssh',\n    label: 'SSH'\n  }];\n  const ipCheckers = [{\n    value: 'IP2Location',\n    label: 'IP2Location'\n  }, {\n    value: 'ip-api',\n    label: 'ip-api'\n  }, {\n    value: 'IPIDEA',\n    label: 'IPIDEA'\n  }, {\n    value: 'IPFoxy',\n    label: 'IPFoxy'\n  }, {\n    value: 'IPInfo',\n    label: 'IPInfo'\n  }];\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleCheckProxy = async () => {\n    if (formData.proxyType === 'no-proxy') {\n      setProxyStatus({\n        success: true,\n        message: 'Local network connection',\n        ip: 'Local IP',\n        location: 'Local Network'\n      });\n      return;\n    }\n    if (!formData.host || !formData.port) {\n      alert('Vui lòng nhập Host và Port');\n      return;\n    }\n    setIsChecking(true);\n    setProxyStatus(null);\n    try {\n      // Use profile service to validate proxy\n      const result = await profileService.validateProxy(formData);\n      if (result.success) {\n        setProxyStatus({\n          success: true,\n          message: 'Proxy connection successful',\n          ip: result.ip_address || 'Unknown',\n          location: result.geolocation ? `${result.geolocation.city}, ${result.geolocation.country}` : 'Unknown Location',\n          responseTime: result.response_time\n        });\n      } else {\n        setProxyStatus({\n          success: false,\n          message: 'Proxy connection failed',\n          error: result.error || 'Unknown error'\n        });\n      }\n    } catch (error) {\n      setProxyStatus({\n        success: false,\n        message: 'Proxy validation failed',\n        error: error.message\n      });\n    } finally {\n      setIsChecking(false);\n    }\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (!formData.profileName) {\n      alert('Vui lòng nhập tên profile');\n      return;\n    }\n    if (formData.proxyType !== 'no-proxy' && (!formData.host || !formData.port)) {\n      alert('Vui lòng nhập thông tin proxy');\n      return;\n    }\n    onSubmit(formData);\n    handleClose();\n  };\n  const handleClose = () => {\n    setFormData({\n      proxyType: 'no-proxy',\n      ipChecker: 'IP2Location',\n      host: '',\n      port: '',\n      username: '',\n      password: '',\n      profileName: ''\n    });\n    setProxyStatus(null);\n    onClose();\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-xl w-full max-w-md mx-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-900\",\n          children: \"T\\u1EA1o Profile M\\u1EDBi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleClose,\n          className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n          children: /*#__PURE__*/_jsxDEV(FiX, {\n            className: \"w-6 h-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"p-6 space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"T\\xEAn Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"profileName\",\n            value: formData.profileName,\n            onChange: handleInputChange,\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n            placeholder: \"Nh\\u1EADp t\\xEAn profile\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Proxy Type\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            name: \"proxyType\",\n            value: formData.proxyType,\n            onChange: handleInputChange,\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n            children: proxyTypes.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: type.value,\n              children: type.label\n            }, type.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"IP Checker\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            name: \"ipChecker\",\n            value: formData.ipChecker,\n            onChange: handleInputChange,\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n            children: ipCheckers.map(checker => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: checker.value,\n              children: checker.label\n            }, checker.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this), formData.proxyType !== 'no-proxy' && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Host\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"host\",\n                value: formData.host,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                placeholder: \"***********\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Port\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"port\",\n                value: formData.port,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                placeholder: \"8080\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Username\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"username\",\n                value: formData.username,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                placeholder: \"proxy username\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"password\",\n                name: \"password\",\n                value: formData.password,\n                onChange: handleInputChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                placeholder: \"proxy password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: handleCheckProxy,\n            disabled: isChecking,\n            className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2\",\n            children: isChecking ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u0110ang ki\\u1EC3m tra...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(FiGlobe, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Ki\\u1EC3m tra Proxy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), proxyStatus && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `p-3 rounded-md ${proxyStatus.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [proxyStatus.success ? /*#__PURE__*/_jsxDEV(FiCheck, {\n              className: \"w-5 h-5 text-green-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(FiX, {\n              className: \"w-5 h-5 text-red-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `text-sm font-medium ${proxyStatus.success ? 'text-green-800' : 'text-red-800'}`,\n              children: proxyStatus.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 15\n          }, this), proxyStatus.success && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2 text-sm text-green-700\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"IP: \", proxyStatus.ip]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Location: \", proxyStatus.location]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 19\n            }, this), proxyStatus.provider && /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Provider: \", proxyStatus.provider]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 44\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 17\n          }, this), !proxyStatus.success && proxyStatus.error && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-sm text-red-700\",\n            children: proxyStatus.error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-4 pt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: handleClose,\n            className: \"flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"flex-1 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors\",\n            children: \"T\\u1EA1o t\\xE0i kho\\u1EA3n\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 131,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfileForm, \"YNJuebSp0fzLS9Pd4wU7MaImc1Q=\");\n_c = ProfileForm;\nexport default ProfileForm;\nvar _c;\n$RefreshReg$(_c, \"ProfileForm\");", "map": {"version": 3, "names": ["React", "useState", "FiX", "<PERSON><PERSON><PERSON><PERSON>", "FiGlobe", "profileService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProfileForm", "isOpen", "onClose", "onSubmit", "_s", "formData", "setFormData", "proxyType", "<PERSON>p<PERSON><PERSON><PERSON>", "host", "port", "username", "password", "profileName", "proxyStatus", "setProxyStatus", "isChecking", "setIsChecking", "proxyTypes", "value", "label", "ipCheckers", "handleInputChange", "e", "name", "target", "prev", "handleCheckProxy", "success", "message", "ip", "location", "alert", "result", "validateProxy", "ip_address", "geolocation", "city", "country", "responseTime", "response_time", "error", "handleSubmit", "preventDefault", "handleClose", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "type", "onChange", "placeholder", "required", "map", "checker", "disabled", "provider", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Dashboard/ProfileForm.jsx"], "sourcesContent": ["/**\n * ProfileForm Component - Form for creating new browser profiles\n */\n\nimport React, { useState } from 'react';\nimport { FiX, FiCheck, FiGlobe } from 'react-icons/fi';\nimport profileService from '../../services/profileService';\n\nconst ProfileForm = ({ isOpen, onClose, onSubmit }) => {\n  const [formData, setFormData] = useState({\n    proxyType: 'no-proxy',\n    ipChecker: 'IP2Location',\n    host: '',\n    port: '',\n    username: '',\n    password: '',\n    profileName: ''\n  });\n  const [proxyStatus, setProxyStatus] = useState(null);\n  const [isChecking, setIsChecking] = useState(false);\n\n  const proxyTypes = [\n    { value: 'no-proxy', label: 'No proxy (local network)' },\n    { value: 'http', label: 'HTTP' },\n    { value: 'https', label: 'HTTPS' },\n    { value: 'socks5', label: 'SOCKS5' },\n    { value: 'ssh', label: 'SSH' }\n  ];\n\n  const ipCheckers = [\n    { value: 'IP2Location', label: 'IP2Location' },\n    { value: 'ip-api', label: 'ip-api' },\n    { value: 'IPIDEA', label: 'IPIDEA' },\n    { value: 'IPFoxy', label: 'IPFoxy' },\n    { value: 'IPInfo', label: 'IPInfo' }\n  ];\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleCheckProxy = async () => {\n    if (formData.proxyType === 'no-proxy') {\n      setProxyStatus({\n        success: true,\n        message: 'Local network connection',\n        ip: 'Local IP',\n        location: 'Local Network'\n      });\n      return;\n    }\n\n    if (!formData.host || !formData.port) {\n      alert('Vui lòng nhập Host và Port');\n      return;\n    }\n\n    setIsChecking(true);\n    setProxyStatus(null);\n\n    try {\n      // Use profile service to validate proxy\n      const result = await profileService.validateProxy(formData);\n\n      if (result.success) {\n        setProxyStatus({\n          success: true,\n          message: 'Proxy connection successful',\n          ip: result.ip_address || 'Unknown',\n          location: result.geolocation ?\n            `${result.geolocation.city}, ${result.geolocation.country}` :\n            'Unknown Location',\n          responseTime: result.response_time\n        });\n      } else {\n        setProxyStatus({\n          success: false,\n          message: 'Proxy connection failed',\n          error: result.error || 'Unknown error'\n        });\n      }\n    } catch (error) {\n      setProxyStatus({\n        success: false,\n        message: 'Proxy validation failed',\n        error: error.message\n      });\n    } finally {\n      setIsChecking(false);\n    }\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    \n    if (!formData.profileName) {\n      alert('Vui lòng nhập tên profile');\n      return;\n    }\n\n    if (formData.proxyType !== 'no-proxy' && (!formData.host || !formData.port)) {\n      alert('Vui lòng nhập thông tin proxy');\n      return;\n    }\n\n    onSubmit(formData);\n    handleClose();\n  };\n\n  const handleClose = () => {\n    setFormData({\n      proxyType: 'no-proxy',\n      ipChecker: 'IP2Location',\n      host: '',\n      port: '',\n      username: '',\n      password: '',\n      profileName: ''\n    });\n    setProxyStatus(null);\n    onClose();\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg shadow-xl w-full max-w-md mx-4\">\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n          <h2 className=\"text-xl font-semibold text-gray-900\">Tạo Profile Mới</h2>\n          <button\n            onClick={handleClose}\n            className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n          >\n            <FiX className=\"w-6 h-6\" />\n          </button>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"p-6 space-y-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Tên Profile\n            </label>\n            <input\n              type=\"text\"\n              name=\"profileName\"\n              value={formData.profileName}\n              onChange={handleInputChange}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              placeholder=\"Nhập tên profile\"\n              required\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Proxy Type\n            </label>\n            <select\n              name=\"proxyType\"\n              value={formData.proxyType}\n              onChange={handleInputChange}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              {proxyTypes.map(type => (\n                <option key={type.value} value={type.value}>\n                  {type.label}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              IP Checker\n            </label>\n            <select\n              name=\"ipChecker\"\n              value={formData.ipChecker}\n              onChange={handleInputChange}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              {ipCheckers.map(checker => (\n                <option key={checker.value} value={checker.value}>\n                  {checker.label}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          {formData.proxyType !== 'no-proxy' && (\n            <>\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Host\n                  </label>\n                  <input\n                    type=\"text\"\n                    name=\"host\"\n                    value={formData.host}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"***********\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Port\n                  </label>\n                  <input\n                    type=\"text\"\n                    name=\"port\"\n                    value={formData.port}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"8080\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Username\n                  </label>\n                  <input\n                    type=\"text\"\n                    name=\"username\"\n                    value={formData.username}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"proxy username\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Password\n                  </label>\n                  <input\n                    type=\"password\"\n                    name=\"password\"\n                    value={formData.password}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"proxy password\"\n                  />\n                </div>\n              </div>\n            </>\n          )}\n\n          <div>\n            <button\n              type=\"button\"\n              onClick={handleCheckProxy}\n              disabled={isChecking}\n              className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2\"\n            >\n              {isChecking ? (\n                <>\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n                  <span>Đang kiểm tra...</span>\n                </>\n              ) : (\n                <>\n                  <FiGlobe className=\"w-4 h-4\" />\n                  <span>Kiểm tra Proxy</span>\n                </>\n              )}\n            </button>\n          </div>\n\n          {proxyStatus && (\n            <div className={`p-3 rounded-md ${proxyStatus.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>\n              <div className=\"flex items-center space-x-2\">\n                {proxyStatus.success ? (\n                  <FiCheck className=\"w-5 h-5 text-green-600\" />\n                ) : (\n                  <FiX className=\"w-5 h-5 text-red-600\" />\n                )}\n                <span className={`text-sm font-medium ${proxyStatus.success ? 'text-green-800' : 'text-red-800'}`}>\n                  {proxyStatus.message}\n                </span>\n              </div>\n              {proxyStatus.success && (\n                <div className=\"mt-2 text-sm text-green-700\">\n                  <p>IP: {proxyStatus.ip}</p>\n                  <p>Location: {proxyStatus.location}</p>\n                  {proxyStatus.provider && <p>Provider: {proxyStatus.provider}</p>}\n                </div>\n              )}\n              {!proxyStatus.success && proxyStatus.error && (\n                <p className=\"mt-2 text-sm text-red-700\">{proxyStatus.error}</p>\n              )}\n            </div>\n          )}\n\n          <div className=\"flex space-x-4 pt-4\">\n            <button\n              type=\"button\"\n              onClick={handleClose}\n              className=\"flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              className=\"flex-1 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors\"\n            >\n              Tạo tài khoản\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default ProfileForm;\n"], "mappings": ";;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,GAAG,EAAEC,OAAO,EAAEC,OAAO,QAAQ,gBAAgB;AACtD,OAAOC,cAAc,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3D,MAAMC,WAAW,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACrD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC;IACvCgB,SAAS,EAAE,UAAU;IACrBC,SAAS,EAAE,aAAa;IACxBC,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAM2B,UAAU,GAAG,CACjB;IAAEC,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAA2B,CAAC,EACxD;IAAED,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAO,CAAC,EAChC;IAAED,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAClC;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,EACpC;IAAED,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAM,CAAC,CAC/B;EAED,MAAMC,UAAU,GAAG,CACjB;IAAEF,KAAK,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAc,CAAC,EAC9C;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,EACpC;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,EACpC;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,EACpC;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,CACrC;EAED,MAAME,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEL;IAAM,CAAC,GAAGI,CAAC,CAACE,MAAM;IAChCnB,WAAW,CAACoB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACF,IAAI,GAAGL;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMQ,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAItB,QAAQ,CAACE,SAAS,KAAK,UAAU,EAAE;MACrCQ,cAAc,CAAC;QACba,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE,0BAA0B;QACnCC,EAAE,EAAE,UAAU;QACdC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF;IACF;IAEA,IAAI,CAAC1B,QAAQ,CAACI,IAAI,IAAI,CAACJ,QAAQ,CAACK,IAAI,EAAE;MACpCsB,KAAK,CAAC,4BAA4B,CAAC;MACnC;IACF;IAEAf,aAAa,CAAC,IAAI,CAAC;IACnBF,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF;MACA,MAAMkB,MAAM,GAAG,MAAMtC,cAAc,CAACuC,aAAa,CAAC7B,QAAQ,CAAC;MAE3D,IAAI4B,MAAM,CAACL,OAAO,EAAE;QAClBb,cAAc,CAAC;UACba,OAAO,EAAE,IAAI;UACbC,OAAO,EAAE,6BAA6B;UACtCC,EAAE,EAAEG,MAAM,CAACE,UAAU,IAAI,SAAS;UAClCJ,QAAQ,EAAEE,MAAM,CAACG,WAAW,GAC1B,GAAGH,MAAM,CAACG,WAAW,CAACC,IAAI,KAAKJ,MAAM,CAACG,WAAW,CAACE,OAAO,EAAE,GAC3D,kBAAkB;UACpBC,YAAY,EAAEN,MAAM,CAACO;QACvB,CAAC,CAAC;MACJ,CAAC,MAAM;QACLzB,cAAc,CAAC;UACba,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE,yBAAyB;UAClCY,KAAK,EAAER,MAAM,CAACQ,KAAK,IAAI;QACzB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd1B,cAAc,CAAC;QACba,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,yBAAyB;QAClCY,KAAK,EAAEA,KAAK,CAACZ;MACf,CAAC,CAAC;IACJ,CAAC,SAAS;MACRZ,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMyB,YAAY,GAAInB,CAAC,IAAK;IAC1BA,CAAC,CAACoB,cAAc,CAAC,CAAC;IAElB,IAAI,CAACtC,QAAQ,CAACQ,WAAW,EAAE;MACzBmB,KAAK,CAAC,2BAA2B,CAAC;MAClC;IACF;IAEA,IAAI3B,QAAQ,CAACE,SAAS,KAAK,UAAU,KAAK,CAACF,QAAQ,CAACI,IAAI,IAAI,CAACJ,QAAQ,CAACK,IAAI,CAAC,EAAE;MAC3EsB,KAAK,CAAC,+BAA+B,CAAC;MACtC;IACF;IAEA7B,QAAQ,CAACE,QAAQ,CAAC;IAClBuC,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMA,WAAW,GAAGA,CAAA,KAAM;IACxBtC,WAAW,CAAC;MACVC,SAAS,EAAE,UAAU;MACrBC,SAAS,EAAE,aAAa;MACxBC,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,EAAE;MACRC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE;IACf,CAAC,CAAC;IACFE,cAAc,CAAC,IAAI,CAAC;IACpBb,OAAO,CAAC,CAAC;EACX,CAAC;EAED,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEJ,OAAA;IAAKgD,SAAS,EAAC,4EAA4E;IAAAC,QAAA,eACzFjD,OAAA;MAAKgD,SAAS,EAAC,oDAAoD;MAAAC,QAAA,gBACjEjD,OAAA;QAAKgD,SAAS,EAAC,gEAAgE;QAAAC,QAAA,gBAC7EjD,OAAA;UAAIgD,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxErD,OAAA;UACEsD,OAAO,EAAEP,WAAY;UACrBC,SAAS,EAAC,qDAAqD;UAAAC,QAAA,eAE/DjD,OAAA,CAACL,GAAG;YAACqD,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENrD,OAAA;QAAMM,QAAQ,EAAEuC,YAAa;QAACG,SAAS,EAAC,eAAe;QAAAC,QAAA,gBACrDjD,OAAA;UAAAiD,QAAA,gBACEjD,OAAA;YAAOgD,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRrD,OAAA;YACEuD,IAAI,EAAC,MAAM;YACX5B,IAAI,EAAC,aAAa;YAClBL,KAAK,EAAEd,QAAQ,CAACQ,WAAY;YAC5BwC,QAAQ,EAAE/B,iBAAkB;YAC5BuB,SAAS,EAAC,wGAAwG;YAClHS,WAAW,EAAC,0BAAkB;YAC9BC,QAAQ;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENrD,OAAA;UAAAiD,QAAA,gBACEjD,OAAA;YAAOgD,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRrD,OAAA;YACE2B,IAAI,EAAC,WAAW;YAChBL,KAAK,EAAEd,QAAQ,CAACE,SAAU;YAC1B8C,QAAQ,EAAE/B,iBAAkB;YAC5BuB,SAAS,EAAC,wGAAwG;YAAAC,QAAA,EAEjH5B,UAAU,CAACsC,GAAG,CAACJ,IAAI,iBAClBvD,OAAA;cAAyBsB,KAAK,EAAEiC,IAAI,CAACjC,KAAM;cAAA2B,QAAA,EACxCM,IAAI,CAAChC;YAAK,GADAgC,IAAI,CAACjC,KAAK;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEf,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENrD,OAAA;UAAAiD,QAAA,gBACEjD,OAAA;YAAOgD,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRrD,OAAA;YACE2B,IAAI,EAAC,WAAW;YAChBL,KAAK,EAAEd,QAAQ,CAACG,SAAU;YAC1B6C,QAAQ,EAAE/B,iBAAkB;YAC5BuB,SAAS,EAAC,wGAAwG;YAAAC,QAAA,EAEjHzB,UAAU,CAACmC,GAAG,CAACC,OAAO,iBACrB5D,OAAA;cAA4BsB,KAAK,EAAEsC,OAAO,CAACtC,KAAM;cAAA2B,QAAA,EAC9CW,OAAO,CAACrC;YAAK,GADHqC,OAAO,CAACtC,KAAK;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAElB,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAEL7C,QAAQ,CAACE,SAAS,KAAK,UAAU,iBAChCV,OAAA,CAAAE,SAAA;UAAA+C,QAAA,gBACEjD,OAAA;YAAKgD,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCjD,OAAA;cAAAiD,QAAA,gBACEjD,OAAA;gBAAOgD,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRrD,OAAA;gBACEuD,IAAI,EAAC,MAAM;gBACX5B,IAAI,EAAC,MAAM;gBACXL,KAAK,EAAEd,QAAQ,CAACI,IAAK;gBACrB4C,QAAQ,EAAE/B,iBAAkB;gBAC5BuB,SAAS,EAAC,wGAAwG;gBAClHS,WAAW,EAAC;cAAa;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrD,OAAA;cAAAiD,QAAA,gBACEjD,OAAA;gBAAOgD,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRrD,OAAA;gBACEuD,IAAI,EAAC,MAAM;gBACX5B,IAAI,EAAC,MAAM;gBACXL,KAAK,EAAEd,QAAQ,CAACK,IAAK;gBACrB2C,QAAQ,EAAE/B,iBAAkB;gBAC5BuB,SAAS,EAAC,wGAAwG;gBAClHS,WAAW,EAAC;cAAM;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrD,OAAA;YAAKgD,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCjD,OAAA;cAAAiD,QAAA,gBACEjD,OAAA;gBAAOgD,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRrD,OAAA;gBACEuD,IAAI,EAAC,MAAM;gBACX5B,IAAI,EAAC,UAAU;gBACfL,KAAK,EAAEd,QAAQ,CAACM,QAAS;gBACzB0C,QAAQ,EAAE/B,iBAAkB;gBAC5BuB,SAAS,EAAC,wGAAwG;gBAClHS,WAAW,EAAC;cAAgB;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrD,OAAA;cAAAiD,QAAA,gBACEjD,OAAA;gBAAOgD,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRrD,OAAA;gBACEuD,IAAI,EAAC,UAAU;gBACf5B,IAAI,EAAC,UAAU;gBACfL,KAAK,EAAEd,QAAQ,CAACO,QAAS;gBACzByC,QAAQ,EAAE/B,iBAAkB;gBAC5BuB,SAAS,EAAC,wGAAwG;gBAClHS,WAAW,EAAC;cAAgB;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,eACN,CACH,eAEDrD,OAAA;UAAAiD,QAAA,eACEjD,OAAA;YACEuD,IAAI,EAAC,QAAQ;YACbD,OAAO,EAAExB,gBAAiB;YAC1B+B,QAAQ,EAAE1C,UAAW;YACrB6B,SAAS,EAAC,iKAAiK;YAAAC,QAAA,EAE1K9B,UAAU,gBACTnB,OAAA,CAAAE,SAAA;cAAA+C,QAAA,gBACEjD,OAAA;gBAAKgD,SAAS,EAAC;cAA2D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjFrD,OAAA;gBAAAiD,QAAA,EAAM;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eAC7B,CAAC,gBAEHrD,OAAA,CAAAE,SAAA;cAAA+C,QAAA,gBACEjD,OAAA,CAACH,OAAO;gBAACmD,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/BrD,OAAA;gBAAAiD,QAAA,EAAM;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eAC3B;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELpC,WAAW,iBACVjB,OAAA;UAAKgD,SAAS,EAAE,kBAAkB/B,WAAW,CAACc,OAAO,GAAG,qCAAqC,GAAG,iCAAiC,EAAG;UAAAkB,QAAA,gBAClIjD,OAAA;YAAKgD,SAAS,EAAC,6BAA6B;YAAAC,QAAA,GACzChC,WAAW,CAACc,OAAO,gBAClB/B,OAAA,CAACJ,OAAO;cAACoD,SAAS,EAAC;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE9CrD,OAAA,CAACL,GAAG;cAACqD,SAAS,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CACxC,eACDrD,OAAA;cAAMgD,SAAS,EAAE,uBAAuB/B,WAAW,CAACc,OAAO,GAAG,gBAAgB,GAAG,cAAc,EAAG;cAAAkB,QAAA,EAC/FhC,WAAW,CAACe;YAAO;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EACLpC,WAAW,CAACc,OAAO,iBAClB/B,OAAA;YAAKgD,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CjD,OAAA;cAAAiD,QAAA,GAAG,MAAI,EAAChC,WAAW,CAACgB,EAAE;YAAA;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3BrD,OAAA;cAAAiD,QAAA,GAAG,YAAU,EAAChC,WAAW,CAACiB,QAAQ;YAAA;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACtCpC,WAAW,CAAC6C,QAAQ,iBAAI9D,OAAA;cAAAiD,QAAA,GAAG,YAAU,EAAChC,WAAW,CAAC6C,QAAQ;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CACN,EACA,CAACpC,WAAW,CAACc,OAAO,IAAId,WAAW,CAAC2B,KAAK,iBACxC5C,OAAA;YAAGgD,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAEhC,WAAW,CAAC2B;UAAK;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAChE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,eAEDrD,OAAA;UAAKgD,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClCjD,OAAA;YACEuD,IAAI,EAAC,QAAQ;YACbD,OAAO,EAAEP,WAAY;YACrBC,SAAS,EAAC,2FAA2F;YAAAC,QAAA,EACtG;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrD,OAAA;YACEuD,IAAI,EAAC,QAAQ;YACbP,SAAS,EAAC,0FAA0F;YAAAC,QAAA,EACrG;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9C,EAAA,CAzTIJ,WAAW;AAAA4D,EAAA,GAAX5D,WAAW;AA2TjB,eAAeA,WAAW;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}