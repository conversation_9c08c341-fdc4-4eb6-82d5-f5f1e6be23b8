{"ast": null, "code": "/**\n * Profile Service - Manages browser profiles and proxy integration\n */\n\nimport { profileAPI, proxyAPI, handleApiError } from './api';\nclass ProfileService {\n  constructor() {\n    this.profiles = [];\n    this.proxies = [];\n    this.listeners = [];\n  }\n\n  // Event listener management\n  addListener(callback) {\n    this.listeners.push(callback);\n  }\n  removeListener(callback) {\n    this.listeners = this.listeners.filter(l => l !== callback);\n  }\n  notifyListeners(event, data) {\n    this.listeners.forEach(listener => {\n      try {\n        listener(event, data);\n      } catch (error) {\n        console.error('Error in profile service listener:', error);\n      }\n    });\n  }\n\n  // Profile management\n  async loadProfiles() {\n    try {\n      const profiles = await profileAPI.getProfiles();\n      this.profiles = profiles.map(profile => this.transformProfileFromAPI(profile));\n\n      // Add mock data if no profiles exist\n      if (this.profiles.length === 0) {\n        this.profiles = this.getMockProfiles();\n      }\n      this.notifyListeners('profiles_loaded', this.profiles);\n      return this.profiles;\n    } catch (error) {\n      console.error('Failed to load profiles from API, using mock data:', error);\n      // Use mock data when API fails\n      this.profiles = this.getMockProfiles();\n      this.notifyListeners('profiles_loaded', this.profiles);\n      return this.profiles;\n    }\n  }\n  getMockProfiles() {\n    return [{\n      id: 1,\n      stt: 1,\n      username: 'profile_001',\n      proxy: '192.168.1.100:8080',\n      status: 'SẴN SÀNG',\n      followersFollowed: 45,\n      followersToday: 12,\n      currentAction: 'Đang theo dõi @competitor1',\n      actions: ['pause', 'stop'],\n      isActive: true,\n      isLoggedIn: true,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    }, {\n      id: 2,\n      stt: 2,\n      username: 'profile_002',\n      proxy: '192.168.1.101:8080',\n      status: 'ĐANG NHẬP HOẠT ĐỘ',\n      followersFollowed: 23,\n      followersToday: 8,\n      currentAction: 'Đang xem video @competitor2',\n      actions: ['pause', 'stop'],\n      isActive: true,\n      isLoggedIn: true,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    }, {\n      id: 3,\n      stt: 3,\n      username: 'profile_003',\n      proxy: 'Local Network',\n      status: 'CHƯA ĐĂNG NHẬP',\n      followersFollowed: 0,\n      followersToday: 0,\n      currentAction: 'Chờ đăng nhập',\n      actions: ['login'],\n      isActive: false,\n      isLoggedIn: false,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    }];\n  }\n  async createProfile(profileData) {\n    try {\n      // First create proxy if needed\n      let proxyId = null;\n      if (profileData.proxyType !== 'no-proxy') {\n        const proxy = await this.createProxyFromForm(profileData);\n        proxyId = proxy.id;\n      }\n\n      // Create profile\n      const apiProfileData = {\n        name: profileData.profileName,\n        description: `Profile with ${profileData.proxyType} proxy`,\n        proxy_id: proxyId,\n        auto_generate_fingerprint: true,\n        os_preference: 'windows',\n        browser_preference: 'firefox'\n      };\n      const newProfile = await profileAPI.createProfile(apiProfileData);\n      const transformedProfile = this.transformProfileFromAPI(newProfile);\n      this.profiles.push(transformedProfile);\n      this.notifyListeners('profile_created', transformedProfile);\n      return transformedProfile;\n    } catch (error) {\n      const apiError = handleApiError(error);\n      console.error('Failed to create profile:', apiError);\n      throw new Error(apiError.message);\n    }\n  }\n  async updateProfile(profileId, updates) {\n    try {\n      const updatedProfile = await profileAPI.updateProfile(profileId, updates);\n      const transformedProfile = this.transformProfileFromAPI(updatedProfile);\n      const index = this.profiles.findIndex(p => p.id === profileId);\n      if (index !== -1) {\n        this.profiles[index] = transformedProfile;\n        this.notifyListeners('profile_updated', transformedProfile);\n      }\n      return transformedProfile;\n    } catch (error) {\n      const apiError = handleApiError(error);\n      console.error('Failed to update profile:', apiError);\n      throw new Error(apiError.message);\n    }\n  }\n  async deleteProfile(profileId) {\n    try {\n      await profileAPI.deleteProfile(profileId);\n      this.profiles = this.profiles.filter(p => p.id !== profileId);\n      this.notifyListeners('profile_deleted', profileId);\n      return true;\n    } catch (error) {\n      const apiError = handleApiError(error);\n      console.error('Failed to delete profile:', apiError);\n      throw new Error(apiError.message);\n    }\n  }\n  async testProfile(profileId) {\n    try {\n      const result = await profileAPI.testProfile(profileId);\n      this.notifyListeners('profile_tested', {\n        profileId,\n        result\n      });\n      return result;\n    } catch (error) {\n      const apiError = handleApiError(error);\n      console.error('Failed to test profile:', apiError);\n      throw new Error(apiError.message);\n    }\n  }\n\n  // Proxy management\n  async createProxyFromForm(formData) {\n    try {\n      const proxyData = {\n        name: `${formData.profileName}_proxy`,\n        proxy_type: formData.proxyType,\n        host: formData.host,\n        port: parseInt(formData.port),\n        username: formData.username || null,\n        password: formData.password || null,\n        description: `Proxy for profile ${formData.profileName}`,\n        validate_on_create: true\n      };\n      const proxy = await proxyAPI.createProxy(proxyData);\n      return proxy;\n    } catch (error) {\n      const apiError = handleApiError(error);\n      console.error('Failed to create proxy:', apiError);\n      throw new Error(apiError.message);\n    }\n  }\n  async validateProxy(proxyData) {\n    try {\n      // Create temporary proxy for validation\n      const tempProxy = await this.createProxyFromForm({\n        ...proxyData,\n        profileName: 'temp_validation'\n      });\n\n      // Validate the proxy\n      const result = await proxyAPI.validateProxy(tempProxy.id);\n\n      // Clean up temporary proxy\n      await proxyAPI.deleteProxy(tempProxy.id);\n      return result;\n    } catch (error) {\n      const apiError = handleApiError(error);\n      console.error('Failed to validate proxy:', apiError);\n      throw new Error(apiError.message);\n    }\n  }\n\n  // Profile actions\n  async startLogin(profileId) {\n    try {\n      // Update profile status\n      await this.updateProfile(profileId, {\n        status: 'logging_in',\n        current_action: 'Đang đăng nhập...'\n      });\n\n      // TODO: Implement actual browser launch for login\n      this.notifyListeners('login_started', profileId);\n      return {\n        success: true,\n        message: 'Login process started'\n      };\n    } catch (error) {\n      console.error('Failed to start login:', error);\n      throw error;\n    }\n  }\n  async completeLogin(profileId) {\n    try {\n      // Update profile status\n      await this.updateProfile(profileId, {\n        status: 'ready',\n        current_action: 'Sẵn sàng chạy',\n        is_logged_in: true\n      });\n      this.notifyListeners('login_completed', profileId);\n      return {\n        success: true,\n        message: 'Login completed successfully'\n      };\n    } catch (error) {\n      console.error('Failed to complete login:', error);\n      throw error;\n    }\n  }\n  async startAutomation(profileId) {\n    try {\n      // Update profile status\n      await this.updateProfile(profileId, {\n        status: 'running',\n        current_action: 'Đang tương tác'\n      });\n      this.notifyListeners('automation_started', profileId);\n      return {\n        success: true,\n        message: 'Automation started'\n      };\n    } catch (error) {\n      console.error('Failed to start automation:', error);\n      throw error;\n    }\n  }\n  async pauseAutomation(profileId) {\n    try {\n      // Update profile status\n      await this.updateProfile(profileId, {\n        status: 'paused',\n        current_action: 'Đã tạm dừng'\n      });\n      this.notifyListeners('automation_paused', profileId);\n      return {\n        success: true,\n        message: 'Automation paused'\n      };\n    } catch (error) {\n      console.error('Failed to pause automation:', error);\n      throw error;\n    }\n  }\n  async stopAutomation(profileId) {\n    try {\n      // Update profile status\n      await this.updateProfile(profileId, {\n        status: 'ready',\n        current_action: 'Đã dừng'\n      });\n      this.notifyListeners('automation_stopped', profileId);\n      return {\n        success: true,\n        message: 'Automation stopped'\n      };\n    } catch (error) {\n      console.error('Failed to stop automation:', error);\n      throw error;\n    }\n  }\n\n  // Bulk operations\n  async bulkAction(profileIds, action) {\n    const results = [];\n    for (const profileId of profileIds) {\n      try {\n        let result;\n        switch (action) {\n          case 'start':\n            result = await this.startAutomation(profileId);\n            break;\n          case 'pause':\n            result = await this.pauseAutomation(profileId);\n            break;\n          case 'stop':\n            result = await this.stopAutomation(profileId);\n            break;\n          default:\n            throw new Error(`Unknown action: ${action}`);\n        }\n        results.push({\n          profileId,\n          success: true,\n          result\n        });\n      } catch (error) {\n        results.push({\n          profileId,\n          success: false,\n          error: error.message\n        });\n      }\n    }\n    this.notifyListeners('bulk_action_completed', {\n      action,\n      results\n    });\n    return results;\n  }\n\n  // Data transformation\n  transformProfileFromAPI(apiProfile) {\n    return {\n      id: apiProfile.id,\n      stt: apiProfile.id,\n      username: apiProfile.name,\n      proxy: apiProfile.proxy_info ? `${apiProfile.proxy_info.host}:${apiProfile.proxy_info.port}` : 'Local Network',\n      status: this.mapStatusToVietnamese(apiProfile.status || 'inactive'),\n      followersFollowed: 0,\n      // TODO: Get from automation data\n      followersToday: 0,\n      // TODO: Get from automation data\n      currentAction: apiProfile.current_action || 'Chờ đăng nhập',\n      actions: this.getAvailableActions(apiProfile),\n      isActive: apiProfile.is_active,\n      isLoggedIn: apiProfile.is_logged_in || false,\n      createdAt: apiProfile.created_at,\n      updatedAt: apiProfile.updated_at\n    };\n  }\n  mapStatusToVietnamese(status) {\n    const statusMap = {\n      'inactive': 'CHƯA ĐĂNG NHẬP',\n      'logging_in': 'ĐANG ĐĂNG NHẬP',\n      'ready': 'SẴN SÀNG',\n      'running': 'ĐANG NHẬP HOẠT ĐỘ',\n      'paused': 'TẠM DỪNG',\n      'error': 'LỖI',\n      'completed': 'ĐÃ HOÀN THÀNH'\n    };\n    return statusMap[status] || status.toUpperCase();\n  }\n  getAvailableActions(profile) {\n    const actions = [];\n    if (!profile.is_logged_in) {\n      actions.push('login');\n    } else if (profile.status === 'logging_in') {\n      actions.push('complete');\n    } else if (profile.status === 'ready') {\n      actions.push('start');\n    } else if (profile.status === 'running') {\n      actions.push('pause', 'stop');\n    } else if (profile.status === 'paused') {\n      actions.push('start', 'stop');\n    }\n    return actions;\n  }\n\n  // Getters\n  getProfiles() {\n    return this.profiles;\n  }\n  getProfile(profileId) {\n    return this.profiles.find(p => p.id === profileId);\n  }\n  getActiveProfiles() {\n    return this.profiles.filter(p => p.isActive);\n  }\n  getLoggedInProfiles() {\n    return this.profiles.filter(p => p.isLoggedIn);\n  }\n}\n\n// Create singleton instance\nconst profileService = new ProfileService();\nexport default profileService;", "map": {"version": 3, "names": ["profileAPI", "proxyAPI", "handleApiError", "ProfileService", "constructor", "profiles", "proxies", "listeners", "addListener", "callback", "push", "removeListener", "filter", "l", "notifyListeners", "event", "data", "for<PERSON>ach", "listener", "error", "console", "loadProfiles", "getProfiles", "map", "profile", "transformProfileFromAPI", "length", "getMockProfiles", "id", "stt", "username", "proxy", "status", "followersFollowed", "followers<PERSON>oday", "currentAction", "actions", "isActive", "isLoggedIn", "createdAt", "Date", "toISOString", "updatedAt", "createProfile", "profileData", "proxyId", "proxyType", "createProxyFromForm", "apiProfileData", "name", "profileName", "description", "proxy_id", "auto_generate_fingerprint", "os_preference", "browser_preference", "newProfile", "transformedProfile", "apiError", "Error", "message", "updateProfile", "profileId", "updates", "updatedProfile", "index", "findIndex", "p", "deleteProfile", "testProfile", "result", "formData", "proxyData", "proxy_type", "host", "port", "parseInt", "password", "validate_on_create", "createProxy", "validateProxy", "tempProxy", "deleteProxy", "startLogin", "current_action", "success", "completeLogin", "is_logged_in", "startAutomation", "pauseAutomation", "stopAutomation", "bulkAction", "profileIds", "action", "results", "apiProfile", "proxy_info", "mapStatusToVietnamese", "getAvailableActions", "is_active", "created_at", "updated_at", "statusMap", "toUpperCase", "getProfile", "find", "getActiveProfiles", "getLoggedInProfiles", "profileService"], "sources": ["/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/services/profileService.js"], "sourcesContent": ["/**\n * Profile Service - Manages browser profiles and proxy integration\n */\n\nimport { profileAPI, proxyAPI, handleApiError } from './api';\n\nclass ProfileService {\n  constructor() {\n    this.profiles = [];\n    this.proxies = [];\n    this.listeners = [];\n  }\n\n  // Event listener management\n  addListener(callback) {\n    this.listeners.push(callback);\n  }\n\n  removeListener(callback) {\n    this.listeners = this.listeners.filter(l => l !== callback);\n  }\n\n  notifyListeners(event, data) {\n    this.listeners.forEach(listener => {\n      try {\n        listener(event, data);\n      } catch (error) {\n        console.error('Error in profile service listener:', error);\n      }\n    });\n  }\n\n  // Profile management\n  async loadProfiles() {\n    try {\n      const profiles = await profileAPI.getProfiles();\n      this.profiles = profiles.map(profile => this.transformProfileFromAPI(profile));\n\n      // Add mock data if no profiles exist\n      if (this.profiles.length === 0) {\n        this.profiles = this.getMockProfiles();\n      }\n\n      this.notifyListeners('profiles_loaded', this.profiles);\n      return this.profiles;\n    } catch (error) {\n      console.error('Failed to load profiles from API, using mock data:', error);\n      // Use mock data when API fails\n      this.profiles = this.getMockProfiles();\n      this.notifyListeners('profiles_loaded', this.profiles);\n      return this.profiles;\n    }\n  }\n\n  getMockProfiles() {\n    return [\n      {\n        id: 1,\n        stt: 1,\n        username: 'profile_001',\n        proxy: '192.168.1.100:8080',\n        status: 'SẴN SÀNG',\n        followersFollowed: 45,\n        followersToday: 12,\n        currentAction: 'Đang theo dõi @competitor1',\n        actions: ['pause', 'stop'],\n        isActive: true,\n        isLoggedIn: true,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      },\n      {\n        id: 2,\n        stt: 2,\n        username: 'profile_002',\n        proxy: '192.168.1.101:8080',\n        status: 'ĐANG NHẬP HOẠT ĐỘ',\n        followersFollowed: 23,\n        followersToday: 8,\n        currentAction: 'Đang xem video @competitor2',\n        actions: ['pause', 'stop'],\n        isActive: true,\n        isLoggedIn: true,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      },\n      {\n        id: 3,\n        stt: 3,\n        username: 'profile_003',\n        proxy: 'Local Network',\n        status: 'CHƯA ĐĂNG NHẬP',\n        followersFollowed: 0,\n        followersToday: 0,\n        currentAction: 'Chờ đăng nhập',\n        actions: ['login'],\n        isActive: false,\n        isLoggedIn: false,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      }\n    ];\n  }\n\n  async createProfile(profileData) {\n    try {\n      // First create proxy if needed\n      let proxyId = null;\n      if (profileData.proxyType !== 'no-proxy') {\n        const proxy = await this.createProxyFromForm(profileData);\n        proxyId = proxy.id;\n      }\n\n      // Create profile\n      const apiProfileData = {\n        name: profileData.profileName,\n        description: `Profile with ${profileData.proxyType} proxy`,\n        proxy_id: proxyId,\n        auto_generate_fingerprint: true,\n        os_preference: 'windows',\n        browser_preference: 'firefox'\n      };\n\n      const newProfile = await profileAPI.createProfile(apiProfileData);\n      const transformedProfile = this.transformProfileFromAPI(newProfile);\n      \n      this.profiles.push(transformedProfile);\n      this.notifyListeners('profile_created', transformedProfile);\n      \n      return transformedProfile;\n    } catch (error) {\n      const apiError = handleApiError(error);\n      console.error('Failed to create profile:', apiError);\n      throw new Error(apiError.message);\n    }\n  }\n\n  async updateProfile(profileId, updates) {\n    try {\n      const updatedProfile = await profileAPI.updateProfile(profileId, updates);\n      const transformedProfile = this.transformProfileFromAPI(updatedProfile);\n      \n      const index = this.profiles.findIndex(p => p.id === profileId);\n      if (index !== -1) {\n        this.profiles[index] = transformedProfile;\n        this.notifyListeners('profile_updated', transformedProfile);\n      }\n      \n      return transformedProfile;\n    } catch (error) {\n      const apiError = handleApiError(error);\n      console.error('Failed to update profile:', apiError);\n      throw new Error(apiError.message);\n    }\n  }\n\n  async deleteProfile(profileId) {\n    try {\n      await profileAPI.deleteProfile(profileId);\n      this.profiles = this.profiles.filter(p => p.id !== profileId);\n      this.notifyListeners('profile_deleted', profileId);\n      return true;\n    } catch (error) {\n      const apiError = handleApiError(error);\n      console.error('Failed to delete profile:', apiError);\n      throw new Error(apiError.message);\n    }\n  }\n\n  async testProfile(profileId) {\n    try {\n      const result = await profileAPI.testProfile(profileId);\n      this.notifyListeners('profile_tested', { profileId, result });\n      return result;\n    } catch (error) {\n      const apiError = handleApiError(error);\n      console.error('Failed to test profile:', apiError);\n      throw new Error(apiError.message);\n    }\n  }\n\n  // Proxy management\n  async createProxyFromForm(formData) {\n    try {\n      const proxyData = {\n        name: `${formData.profileName}_proxy`,\n        proxy_type: formData.proxyType,\n        host: formData.host,\n        port: parseInt(formData.port),\n        username: formData.username || null,\n        password: formData.password || null,\n        description: `Proxy for profile ${formData.profileName}`,\n        validate_on_create: true\n      };\n\n      const proxy = await proxyAPI.createProxy(proxyData);\n      return proxy;\n    } catch (error) {\n      const apiError = handleApiError(error);\n      console.error('Failed to create proxy:', apiError);\n      throw new Error(apiError.message);\n    }\n  }\n\n  async validateProxy(proxyData) {\n    try {\n      // Create temporary proxy for validation\n      const tempProxy = await this.createProxyFromForm({\n        ...proxyData,\n        profileName: 'temp_validation'\n      });\n\n      // Validate the proxy\n      const result = await proxyAPI.validateProxy(tempProxy.id);\n      \n      // Clean up temporary proxy\n      await proxyAPI.deleteProxy(tempProxy.id);\n      \n      return result;\n    } catch (error) {\n      const apiError = handleApiError(error);\n      console.error('Failed to validate proxy:', apiError);\n      throw new Error(apiError.message);\n    }\n  }\n\n  // Profile actions\n  async startLogin(profileId) {\n    try {\n      // Update profile status\n      await this.updateProfile(profileId, { \n        status: 'logging_in',\n        current_action: 'Đang đăng nhập...'\n      });\n\n      // TODO: Implement actual browser launch for login\n      this.notifyListeners('login_started', profileId);\n      \n      return { success: true, message: 'Login process started' };\n    } catch (error) {\n      console.error('Failed to start login:', error);\n      throw error;\n    }\n  }\n\n  async completeLogin(profileId) {\n    try {\n      // Update profile status\n      await this.updateProfile(profileId, { \n        status: 'ready',\n        current_action: 'Sẵn sàng chạy',\n        is_logged_in: true\n      });\n\n      this.notifyListeners('login_completed', profileId);\n      \n      return { success: true, message: 'Login completed successfully' };\n    } catch (error) {\n      console.error('Failed to complete login:', error);\n      throw error;\n    }\n  }\n\n  async startAutomation(profileId) {\n    try {\n      // Update profile status\n      await this.updateProfile(profileId, { \n        status: 'running',\n        current_action: 'Đang tương tác'\n      });\n\n      this.notifyListeners('automation_started', profileId);\n      \n      return { success: true, message: 'Automation started' };\n    } catch (error) {\n      console.error('Failed to start automation:', error);\n      throw error;\n    }\n  }\n\n  async pauseAutomation(profileId) {\n    try {\n      // Update profile status\n      await this.updateProfile(profileId, { \n        status: 'paused',\n        current_action: 'Đã tạm dừng'\n      });\n\n      this.notifyListeners('automation_paused', profileId);\n      \n      return { success: true, message: 'Automation paused' };\n    } catch (error) {\n      console.error('Failed to pause automation:', error);\n      throw error;\n    }\n  }\n\n  async stopAutomation(profileId) {\n    try {\n      // Update profile status\n      await this.updateProfile(profileId, { \n        status: 'ready',\n        current_action: 'Đã dừng'\n      });\n\n      this.notifyListeners('automation_stopped', profileId);\n      \n      return { success: true, message: 'Automation stopped' };\n    } catch (error) {\n      console.error('Failed to stop automation:', error);\n      throw error;\n    }\n  }\n\n  // Bulk operations\n  async bulkAction(profileIds, action) {\n    const results = [];\n    \n    for (const profileId of profileIds) {\n      try {\n        let result;\n        switch (action) {\n          case 'start':\n            result = await this.startAutomation(profileId);\n            break;\n          case 'pause':\n            result = await this.pauseAutomation(profileId);\n            break;\n          case 'stop':\n            result = await this.stopAutomation(profileId);\n            break;\n          default:\n            throw new Error(`Unknown action: ${action}`);\n        }\n        results.push({ profileId, success: true, result });\n      } catch (error) {\n        results.push({ profileId, success: false, error: error.message });\n      }\n    }\n\n    this.notifyListeners('bulk_action_completed', { action, results });\n    return results;\n  }\n\n  // Data transformation\n  transformProfileFromAPI(apiProfile) {\n    return {\n      id: apiProfile.id,\n      stt: apiProfile.id,\n      username: apiProfile.name,\n      proxy: apiProfile.proxy_info ? \n        `${apiProfile.proxy_info.host}:${apiProfile.proxy_info.port}` : \n        'Local Network',\n      status: this.mapStatusToVietnamese(apiProfile.status || 'inactive'),\n      followersFollowed: 0, // TODO: Get from automation data\n      followersToday: 0, // TODO: Get from automation data\n      currentAction: apiProfile.current_action || 'Chờ đăng nhập',\n      actions: this.getAvailableActions(apiProfile),\n      isActive: apiProfile.is_active,\n      isLoggedIn: apiProfile.is_logged_in || false,\n      createdAt: apiProfile.created_at,\n      updatedAt: apiProfile.updated_at\n    };\n  }\n\n  mapStatusToVietnamese(status) {\n    const statusMap = {\n      'inactive': 'CHƯA ĐĂNG NHẬP',\n      'logging_in': 'ĐANG ĐĂNG NHẬP',\n      'ready': 'SẴN SÀNG',\n      'running': 'ĐANG NHẬP HOẠT ĐỘ',\n      'paused': 'TẠM DỪNG',\n      'error': 'LỖI',\n      'completed': 'ĐÃ HOÀN THÀNH'\n    };\n    return statusMap[status] || status.toUpperCase();\n  }\n\n  getAvailableActions(profile) {\n    const actions = [];\n    \n    if (!profile.is_logged_in) {\n      actions.push('login');\n    } else if (profile.status === 'logging_in') {\n      actions.push('complete');\n    } else if (profile.status === 'ready') {\n      actions.push('start');\n    } else if (profile.status === 'running') {\n      actions.push('pause', 'stop');\n    } else if (profile.status === 'paused') {\n      actions.push('start', 'stop');\n    }\n    \n    return actions;\n  }\n\n  // Getters\n  getProfiles() {\n    return this.profiles;\n  }\n\n  getProfile(profileId) {\n    return this.profiles.find(p => p.id === profileId);\n  }\n\n  getActiveProfiles() {\n    return this.profiles.filter(p => p.isActive);\n  }\n\n  getLoggedInProfiles() {\n    return this.profiles.filter(p => p.isLoggedIn);\n  }\n}\n\n// Create singleton instance\nconst profileService = new ProfileService();\n\nexport default profileService;\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SAASA,UAAU,EAAEC,QAAQ,EAAEC,cAAc,QAAQ,OAAO;AAE5D,MAAMC,cAAc,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,SAAS,GAAG,EAAE;EACrB;;EAEA;EACAC,WAAWA,CAACC,QAAQ,EAAE;IACpB,IAAI,CAACF,SAAS,CAACG,IAAI,CAACD,QAAQ,CAAC;EAC/B;EAEAE,cAAcA,CAACF,QAAQ,EAAE;IACvB,IAAI,CAACF,SAAS,GAAG,IAAI,CAACA,SAAS,CAACK,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKJ,QAAQ,CAAC;EAC7D;EAEAK,eAAeA,CAACC,KAAK,EAAEC,IAAI,EAAE;IAC3B,IAAI,CAACT,SAAS,CAACU,OAAO,CAACC,QAAQ,IAAI;MACjC,IAAI;QACFA,QAAQ,CAACH,KAAK,EAAEC,IAAI,CAAC;MACvB,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC5D;IACF,CAAC,CAAC;EACJ;;EAEA;EACA,MAAME,YAAYA,CAAA,EAAG;IACnB,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAML,UAAU,CAACsB,WAAW,CAAC,CAAC;MAC/C,IAAI,CAACjB,QAAQ,GAAGA,QAAQ,CAACkB,GAAG,CAACC,OAAO,IAAI,IAAI,CAACC,uBAAuB,CAACD,OAAO,CAAC,CAAC;;MAE9E;MACA,IAAI,IAAI,CAACnB,QAAQ,CAACqB,MAAM,KAAK,CAAC,EAAE;QAC9B,IAAI,CAACrB,QAAQ,GAAG,IAAI,CAACsB,eAAe,CAAC,CAAC;MACxC;MAEA,IAAI,CAACb,eAAe,CAAC,iBAAiB,EAAE,IAAI,CAACT,QAAQ,CAAC;MACtD,OAAO,IAAI,CAACA,QAAQ;IACtB,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;MAC1E;MACA,IAAI,CAACd,QAAQ,GAAG,IAAI,CAACsB,eAAe,CAAC,CAAC;MACtC,IAAI,CAACb,eAAe,CAAC,iBAAiB,EAAE,IAAI,CAACT,QAAQ,CAAC;MACtD,OAAO,IAAI,CAACA,QAAQ;IACtB;EACF;EAEAsB,eAAeA,CAAA,EAAG;IAChB,OAAO,CACL;MACEC,EAAE,EAAE,CAAC;MACLC,GAAG,EAAE,CAAC;MACNC,QAAQ,EAAE,aAAa;MACvBC,KAAK,EAAE,oBAAoB;MAC3BC,MAAM,EAAE,UAAU;MAClBC,iBAAiB,EAAE,EAAE;MACrBC,cAAc,EAAE,EAAE;MAClBC,aAAa,EAAE,4BAA4B;MAC3CC,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;MAC1BC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC,EACD;MACEb,EAAE,EAAE,CAAC;MACLC,GAAG,EAAE,CAAC;MACNC,QAAQ,EAAE,aAAa;MACvBC,KAAK,EAAE,oBAAoB;MAC3BC,MAAM,EAAE,mBAAmB;MAC3BC,iBAAiB,EAAE,EAAE;MACrBC,cAAc,EAAE,CAAC;MACjBC,aAAa,EAAE,6BAA6B;MAC5CC,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;MAC1BC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC,EACD;MACEb,EAAE,EAAE,CAAC;MACLC,GAAG,EAAE,CAAC;MACNC,QAAQ,EAAE,aAAa;MACvBC,KAAK,EAAE,eAAe;MACtBC,MAAM,EAAE,gBAAgB;MACxBC,iBAAiB,EAAE,CAAC;MACpBC,cAAc,EAAE,CAAC;MACjBC,aAAa,EAAE,eAAe;MAC9BC,OAAO,EAAE,CAAC,OAAO,CAAC;MAClBC,QAAQ,EAAE,KAAK;MACfC,UAAU,EAAE,KAAK;MACjBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC,CACF;EACH;EAEA,MAAME,aAAaA,CAACC,WAAW,EAAE;IAC/B,IAAI;MACF;MACA,IAAIC,OAAO,GAAG,IAAI;MAClB,IAAID,WAAW,CAACE,SAAS,KAAK,UAAU,EAAE;QACxC,MAAMf,KAAK,GAAG,MAAM,IAAI,CAACgB,mBAAmB,CAACH,WAAW,CAAC;QACzDC,OAAO,GAAGd,KAAK,CAACH,EAAE;MACpB;;MAEA;MACA,MAAMoB,cAAc,GAAG;QACrBC,IAAI,EAAEL,WAAW,CAACM,WAAW;QAC7BC,WAAW,EAAE,gBAAgBP,WAAW,CAACE,SAAS,QAAQ;QAC1DM,QAAQ,EAAEP,OAAO;QACjBQ,yBAAyB,EAAE,IAAI;QAC/BC,aAAa,EAAE,SAAS;QACxBC,kBAAkB,EAAE;MACtB,CAAC;MAED,MAAMC,UAAU,GAAG,MAAMxD,UAAU,CAAC2C,aAAa,CAACK,cAAc,CAAC;MACjE,MAAMS,kBAAkB,GAAG,IAAI,CAAChC,uBAAuB,CAAC+B,UAAU,CAAC;MAEnE,IAAI,CAACnD,QAAQ,CAACK,IAAI,CAAC+C,kBAAkB,CAAC;MACtC,IAAI,CAAC3C,eAAe,CAAC,iBAAiB,EAAE2C,kBAAkB,CAAC;MAE3D,OAAOA,kBAAkB;IAC3B,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACd,MAAMuC,QAAQ,GAAGxD,cAAc,CAACiB,KAAK,CAAC;MACtCC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEuC,QAAQ,CAAC;MACpD,MAAM,IAAIC,KAAK,CAACD,QAAQ,CAACE,OAAO,CAAC;IACnC;EACF;EAEA,MAAMC,aAAaA,CAACC,SAAS,EAAEC,OAAO,EAAE;IACtC,IAAI;MACF,MAAMC,cAAc,GAAG,MAAMhE,UAAU,CAAC6D,aAAa,CAACC,SAAS,EAAEC,OAAO,CAAC;MACzE,MAAMN,kBAAkB,GAAG,IAAI,CAAChC,uBAAuB,CAACuC,cAAc,CAAC;MAEvE,MAAMC,KAAK,GAAG,IAAI,CAAC5D,QAAQ,CAAC6D,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACvC,EAAE,KAAKkC,SAAS,CAAC;MAC9D,IAAIG,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,IAAI,CAAC5D,QAAQ,CAAC4D,KAAK,CAAC,GAAGR,kBAAkB;QACzC,IAAI,CAAC3C,eAAe,CAAC,iBAAiB,EAAE2C,kBAAkB,CAAC;MAC7D;MAEA,OAAOA,kBAAkB;IAC3B,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACd,MAAMuC,QAAQ,GAAGxD,cAAc,CAACiB,KAAK,CAAC;MACtCC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEuC,QAAQ,CAAC;MACpD,MAAM,IAAIC,KAAK,CAACD,QAAQ,CAACE,OAAO,CAAC;IACnC;EACF;EAEA,MAAMQ,aAAaA,CAACN,SAAS,EAAE;IAC7B,IAAI;MACF,MAAM9D,UAAU,CAACoE,aAAa,CAACN,SAAS,CAAC;MACzC,IAAI,CAACzD,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACO,MAAM,CAACuD,CAAC,IAAIA,CAAC,CAACvC,EAAE,KAAKkC,SAAS,CAAC;MAC7D,IAAI,CAAChD,eAAe,CAAC,iBAAiB,EAAEgD,SAAS,CAAC;MAClD,OAAO,IAAI;IACb,CAAC,CAAC,OAAO3C,KAAK,EAAE;MACd,MAAMuC,QAAQ,GAAGxD,cAAc,CAACiB,KAAK,CAAC;MACtCC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEuC,QAAQ,CAAC;MACpD,MAAM,IAAIC,KAAK,CAACD,QAAQ,CAACE,OAAO,CAAC;IACnC;EACF;EAEA,MAAMS,WAAWA,CAACP,SAAS,EAAE;IAC3B,IAAI;MACF,MAAMQ,MAAM,GAAG,MAAMtE,UAAU,CAACqE,WAAW,CAACP,SAAS,CAAC;MACtD,IAAI,CAAChD,eAAe,CAAC,gBAAgB,EAAE;QAAEgD,SAAS;QAAEQ;MAAO,CAAC,CAAC;MAC7D,OAAOA,MAAM;IACf,CAAC,CAAC,OAAOnD,KAAK,EAAE;MACd,MAAMuC,QAAQ,GAAGxD,cAAc,CAACiB,KAAK,CAAC;MACtCC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEuC,QAAQ,CAAC;MAClD,MAAM,IAAIC,KAAK,CAACD,QAAQ,CAACE,OAAO,CAAC;IACnC;EACF;;EAEA;EACA,MAAMb,mBAAmBA,CAACwB,QAAQ,EAAE;IAClC,IAAI;MACF,MAAMC,SAAS,GAAG;QAChBvB,IAAI,EAAE,GAAGsB,QAAQ,CAACrB,WAAW,QAAQ;QACrCuB,UAAU,EAAEF,QAAQ,CAACzB,SAAS;QAC9B4B,IAAI,EAAEH,QAAQ,CAACG,IAAI;QACnBC,IAAI,EAAEC,QAAQ,CAACL,QAAQ,CAACI,IAAI,CAAC;QAC7B7C,QAAQ,EAAEyC,QAAQ,CAACzC,QAAQ,IAAI,IAAI;QACnC+C,QAAQ,EAAEN,QAAQ,CAACM,QAAQ,IAAI,IAAI;QACnC1B,WAAW,EAAE,qBAAqBoB,QAAQ,CAACrB,WAAW,EAAE;QACxD4B,kBAAkB,EAAE;MACtB,CAAC;MAED,MAAM/C,KAAK,GAAG,MAAM9B,QAAQ,CAAC8E,WAAW,CAACP,SAAS,CAAC;MACnD,OAAOzC,KAAK;IACd,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd,MAAMuC,QAAQ,GAAGxD,cAAc,CAACiB,KAAK,CAAC;MACtCC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEuC,QAAQ,CAAC;MAClD,MAAM,IAAIC,KAAK,CAACD,QAAQ,CAACE,OAAO,CAAC;IACnC;EACF;EAEA,MAAMoB,aAAaA,CAACR,SAAS,EAAE;IAC7B,IAAI;MACF;MACA,MAAMS,SAAS,GAAG,MAAM,IAAI,CAAClC,mBAAmB,CAAC;QAC/C,GAAGyB,SAAS;QACZtB,WAAW,EAAE;MACf,CAAC,CAAC;;MAEF;MACA,MAAMoB,MAAM,GAAG,MAAMrE,QAAQ,CAAC+E,aAAa,CAACC,SAAS,CAACrD,EAAE,CAAC;;MAEzD;MACA,MAAM3B,QAAQ,CAACiF,WAAW,CAACD,SAAS,CAACrD,EAAE,CAAC;MAExC,OAAO0C,MAAM;IACf,CAAC,CAAC,OAAOnD,KAAK,EAAE;MACd,MAAMuC,QAAQ,GAAGxD,cAAc,CAACiB,KAAK,CAAC;MACtCC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEuC,QAAQ,CAAC;MACpD,MAAM,IAAIC,KAAK,CAACD,QAAQ,CAACE,OAAO,CAAC;IACnC;EACF;;EAEA;EACA,MAAMuB,UAAUA,CAACrB,SAAS,EAAE;IAC1B,IAAI;MACF;MACA,MAAM,IAAI,CAACD,aAAa,CAACC,SAAS,EAAE;QAClC9B,MAAM,EAAE,YAAY;QACpBoD,cAAc,EAAE;MAClB,CAAC,CAAC;;MAEF;MACA,IAAI,CAACtE,eAAe,CAAC,eAAe,EAAEgD,SAAS,CAAC;MAEhD,OAAO;QAAEuB,OAAO,EAAE,IAAI;QAAEzB,OAAO,EAAE;MAAwB,CAAC;IAC5D,CAAC,CAAC,OAAOzC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb;EACF;EAEA,MAAMmE,aAAaA,CAACxB,SAAS,EAAE;IAC7B,IAAI;MACF;MACA,MAAM,IAAI,CAACD,aAAa,CAACC,SAAS,EAAE;QAClC9B,MAAM,EAAE,OAAO;QACfoD,cAAc,EAAE,eAAe;QAC/BG,YAAY,EAAE;MAChB,CAAC,CAAC;MAEF,IAAI,CAACzE,eAAe,CAAC,iBAAiB,EAAEgD,SAAS,CAAC;MAElD,OAAO;QAAEuB,OAAO,EAAE,IAAI;QAAEzB,OAAO,EAAE;MAA+B,CAAC;IACnE,CAAC,CAAC,OAAOzC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF;EAEA,MAAMqE,eAAeA,CAAC1B,SAAS,EAAE;IAC/B,IAAI;MACF;MACA,MAAM,IAAI,CAACD,aAAa,CAACC,SAAS,EAAE;QAClC9B,MAAM,EAAE,SAAS;QACjBoD,cAAc,EAAE;MAClB,CAAC,CAAC;MAEF,IAAI,CAACtE,eAAe,CAAC,oBAAoB,EAAEgD,SAAS,CAAC;MAErD,OAAO;QAAEuB,OAAO,EAAE,IAAI;QAAEzB,OAAO,EAAE;MAAqB,CAAC;IACzD,CAAC,CAAC,OAAOzC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK;IACb;EACF;EAEA,MAAMsE,eAAeA,CAAC3B,SAAS,EAAE;IAC/B,IAAI;MACF;MACA,MAAM,IAAI,CAACD,aAAa,CAACC,SAAS,EAAE;QAClC9B,MAAM,EAAE,QAAQ;QAChBoD,cAAc,EAAE;MAClB,CAAC,CAAC;MAEF,IAAI,CAACtE,eAAe,CAAC,mBAAmB,EAAEgD,SAAS,CAAC;MAEpD,OAAO;QAAEuB,OAAO,EAAE,IAAI;QAAEzB,OAAO,EAAE;MAAoB,CAAC;IACxD,CAAC,CAAC,OAAOzC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK;IACb;EACF;EAEA,MAAMuE,cAAcA,CAAC5B,SAAS,EAAE;IAC9B,IAAI;MACF;MACA,MAAM,IAAI,CAACD,aAAa,CAACC,SAAS,EAAE;QAClC9B,MAAM,EAAE,OAAO;QACfoD,cAAc,EAAE;MAClB,CAAC,CAAC;MAEF,IAAI,CAACtE,eAAe,CAAC,oBAAoB,EAAEgD,SAAS,CAAC;MAErD,OAAO;QAAEuB,OAAO,EAAE,IAAI;QAAEzB,OAAO,EAAE;MAAqB,CAAC;IACzD,CAAC,CAAC,OAAOzC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAMwE,UAAUA,CAACC,UAAU,EAAEC,MAAM,EAAE;IACnC,MAAMC,OAAO,GAAG,EAAE;IAElB,KAAK,MAAMhC,SAAS,IAAI8B,UAAU,EAAE;MAClC,IAAI;QACF,IAAItB,MAAM;QACV,QAAQuB,MAAM;UACZ,KAAK,OAAO;YACVvB,MAAM,GAAG,MAAM,IAAI,CAACkB,eAAe,CAAC1B,SAAS,CAAC;YAC9C;UACF,KAAK,OAAO;YACVQ,MAAM,GAAG,MAAM,IAAI,CAACmB,eAAe,CAAC3B,SAAS,CAAC;YAC9C;UACF,KAAK,MAAM;YACTQ,MAAM,GAAG,MAAM,IAAI,CAACoB,cAAc,CAAC5B,SAAS,CAAC;YAC7C;UACF;YACE,MAAM,IAAIH,KAAK,CAAC,mBAAmBkC,MAAM,EAAE,CAAC;QAChD;QACAC,OAAO,CAACpF,IAAI,CAAC;UAAEoD,SAAS;UAAEuB,OAAO,EAAE,IAAI;UAAEf;QAAO,CAAC,CAAC;MACpD,CAAC,CAAC,OAAOnD,KAAK,EAAE;QACd2E,OAAO,CAACpF,IAAI,CAAC;UAAEoD,SAAS;UAAEuB,OAAO,EAAE,KAAK;UAAElE,KAAK,EAAEA,KAAK,CAACyC;QAAQ,CAAC,CAAC;MACnE;IACF;IAEA,IAAI,CAAC9C,eAAe,CAAC,uBAAuB,EAAE;MAAE+E,MAAM;MAAEC;IAAQ,CAAC,CAAC;IAClE,OAAOA,OAAO;EAChB;;EAEA;EACArE,uBAAuBA,CAACsE,UAAU,EAAE;IAClC,OAAO;MACLnE,EAAE,EAAEmE,UAAU,CAACnE,EAAE;MACjBC,GAAG,EAAEkE,UAAU,CAACnE,EAAE;MAClBE,QAAQ,EAAEiE,UAAU,CAAC9C,IAAI;MACzBlB,KAAK,EAAEgE,UAAU,CAACC,UAAU,GAC1B,GAAGD,UAAU,CAACC,UAAU,CAACtB,IAAI,IAAIqB,UAAU,CAACC,UAAU,CAACrB,IAAI,EAAE,GAC7D,eAAe;MACjB3C,MAAM,EAAE,IAAI,CAACiE,qBAAqB,CAACF,UAAU,CAAC/D,MAAM,IAAI,UAAU,CAAC;MACnEC,iBAAiB,EAAE,CAAC;MAAE;MACtBC,cAAc,EAAE,CAAC;MAAE;MACnBC,aAAa,EAAE4D,UAAU,CAACX,cAAc,IAAI,eAAe;MAC3DhD,OAAO,EAAE,IAAI,CAAC8D,mBAAmB,CAACH,UAAU,CAAC;MAC7C1D,QAAQ,EAAE0D,UAAU,CAACI,SAAS;MAC9B7D,UAAU,EAAEyD,UAAU,CAACR,YAAY,IAAI,KAAK;MAC5ChD,SAAS,EAAEwD,UAAU,CAACK,UAAU;MAChC1D,SAAS,EAAEqD,UAAU,CAACM;IACxB,CAAC;EACH;EAEAJ,qBAAqBA,CAACjE,MAAM,EAAE;IAC5B,MAAMsE,SAAS,GAAG;MAChB,UAAU,EAAE,gBAAgB;MAC5B,YAAY,EAAE,gBAAgB;MAC9B,OAAO,EAAE,UAAU;MACnB,SAAS,EAAE,mBAAmB;MAC9B,QAAQ,EAAE,UAAU;MACpB,OAAO,EAAE,KAAK;MACd,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,SAAS,CAACtE,MAAM,CAAC,IAAIA,MAAM,CAACuE,WAAW,CAAC,CAAC;EAClD;EAEAL,mBAAmBA,CAAC1E,OAAO,EAAE;IAC3B,MAAMY,OAAO,GAAG,EAAE;IAElB,IAAI,CAACZ,OAAO,CAAC+D,YAAY,EAAE;MACzBnD,OAAO,CAAC1B,IAAI,CAAC,OAAO,CAAC;IACvB,CAAC,MAAM,IAAIc,OAAO,CAACQ,MAAM,KAAK,YAAY,EAAE;MAC1CI,OAAO,CAAC1B,IAAI,CAAC,UAAU,CAAC;IAC1B,CAAC,MAAM,IAAIc,OAAO,CAACQ,MAAM,KAAK,OAAO,EAAE;MACrCI,OAAO,CAAC1B,IAAI,CAAC,OAAO,CAAC;IACvB,CAAC,MAAM,IAAIc,OAAO,CAACQ,MAAM,KAAK,SAAS,EAAE;MACvCI,OAAO,CAAC1B,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC;IAC/B,CAAC,MAAM,IAAIc,OAAO,CAACQ,MAAM,KAAK,QAAQ,EAAE;MACtCI,OAAO,CAAC1B,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC;IAC/B;IAEA,OAAO0B,OAAO;EAChB;;EAEA;EACAd,WAAWA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACjB,QAAQ;EACtB;EAEAmG,UAAUA,CAAC1C,SAAS,EAAE;IACpB,OAAO,IAAI,CAACzD,QAAQ,CAACoG,IAAI,CAACtC,CAAC,IAAIA,CAAC,CAACvC,EAAE,KAAKkC,SAAS,CAAC;EACpD;EAEA4C,iBAAiBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACrG,QAAQ,CAACO,MAAM,CAACuD,CAAC,IAAIA,CAAC,CAAC9B,QAAQ,CAAC;EAC9C;EAEAsE,mBAAmBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACtG,QAAQ,CAACO,MAAM,CAACuD,CAAC,IAAIA,CAAC,CAAC7B,UAAU,CAAC;EAChD;AACF;;AAEA;AACA,MAAMsE,cAAc,GAAG,IAAIzG,cAAc,CAAC,CAAC;AAE3C,eAAeyG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}