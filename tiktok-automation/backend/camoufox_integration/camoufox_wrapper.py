"""
Camoufox Wrapper with GitHub API rate limit handling
"""

import asyncio
import time
import os
from typing import Dict, Any, Optional
from loguru import logger

try:
    from camoufox.async_api import AsyncCamoufox
    CAMOUFOX_AVAILABLE = True
except ImportError:
    CAMOUFOX_AVAILABLE = False
    logger.warning("Camoufox not available, using fallback browser")

from playwright.async_api import async_playwright


class CamoufoxWrapper:
    """Wrapper for Camoufox with rate limit handling and fallbacks"""
    
    def __init__(self):
        self._last_github_request = 0
        self._github_rate_limit_delay = 60
        self._github_cache = {}
    
    async def create_browser(
        self,
        config: Optional[Dict[str, Any]] = None,
        proxy: Optional[Dict[str, Any]] = None,
        headless: bool = True,
        user_data_dir: Optional[str] = None,
        timeout: int = 30000,
        **kwargs
    ):
        """Create browser with fallback mechanisms"""
        
        if not CAMOUFOX_AVAILABLE:
            logger.warning("Camoufox not available, using Playwright fallback")
            return await self._create_playwright_browser(proxy, headless, user_data_dir, **kwargs)
        
        # Try Camoufox with rate limit handling
        try:
            return await self._create_camoufox_browser(
                config, proxy, headless, user_data_dir, timeout, **kwargs
            )
        except Exception as e:
            error_msg = str(e).lower()
            
            if "rate limit" in error_msg or "403" in error_msg:
                logger.warning(f"GitHub API rate limit hit: {e}")
                logger.info("Falling back to Playwright browser")
                return await self._create_playwright_browser(proxy, headless, user_data_dir, **kwargs)
            else:
                raise e
    
    async def _create_camoufox_browser(
        self,
        config: Optional[Dict[str, Any]],
        proxy: Optional[Dict[str, Any]],
        headless: bool,
        user_data_dir: Optional[str],
        timeout: int,
        **kwargs
    ):
        """Create Camoufox browser with rate limit protection"""
        
        # Rate limit protection
        current_time = time.time()
        if current_time - self._last_github_request < self._github_rate_limit_delay:
            wait_time = self._github_rate_limit_delay - (current_time - self._last_github_request)
            logger.info(f"Rate limit protection: waiting {wait_time:.1f}s")
            await asyncio.sleep(wait_time)
        
        self._last_github_request = time.time()
        
        # Use minimal config to reduce GitHub API calls
        minimal_config = config or {
            "os": "windows",
            "screen": {"width": 1920, "height": 1080},
            "locale": "en-US",
            "timezone": "America/New_York"
        }
        
        camoufox = AsyncCamoufox(
            config=minimal_config,
            proxy=proxy,
            headless=headless,
            user_data_dir=user_data_dir,
            timeout=timeout,
            **kwargs
        )
        
        browser = await camoufox.__aenter__()
        logger.info("Camoufox browser created successfully")
        return browser
    
    async def _create_playwright_browser(
        self,
        proxy: Optional[Dict[str, Any]],
        headless: bool,
        user_data_dir: Optional[str],
        **kwargs
    ):
        """Fallback to regular Playwright browser"""
        
        playwright = await async_playwright().start()
        
        # Browser options
        browser_options = {
            "headless": headless,
            "args": [
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--disable-features=TranslateUI",
                "--disable-ipc-flooding-protection",
                "--disable-blink-features=AutomationControlled",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor"
            ]
        }
        
        # Add user data dir if provided
        if user_data_dir:
            browser_options["user_data_dir"] = user_data_dir
        
        # Add proxy if provided
        if proxy and proxy.get("server"):
            browser_options["proxy"] = proxy
        
        browser = await playwright.chromium.launch(**browser_options)
        logger.info("Playwright fallback browser created successfully")
        return browser


# Global instance
camoufox_wrapper = CamoufoxWrapper()
