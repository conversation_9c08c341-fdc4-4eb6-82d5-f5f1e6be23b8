/**
 * API Service for TikTok Automation Backend
 */

import axios from 'axios';

// Get backend URL
const getBackendUrl = async () => {
  if (window.electronAPI) {
    return await window.electronAPI.getBackendUrl();
  }
  return 'http://localhost:8000';
};

// Create axios instance
const createApiInstance = async () => {
  const baseURL = await getBackendUrl();
  return axios.create({
    baseURL: `${baseURL}/api/v1`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

// Profile API
export const profileAPI = {
  // Get all profiles
  async getProfiles(params = {}) {
    const api = await createApiInstance();
    const response = await api.get('/profiles/', { params });
    return response.data;
  },

  // Get profile by ID
  async getProfile(profileId) {
    const api = await createApiInstance();
    const response = await api.get(`/profiles/${profileId}`);
    return response.data;
  },

  // Create new profile
  async createProfile(profileData) {
    const api = await createApiInstance();
    const response = await api.post('/profiles/', profileData);
    return response.data;
  },

  // Update profile
  async updateProfile(profileId, updates) {
    const api = await createApiInstance();
    const response = await api.put(`/profiles/${profileId}`, updates);
    return response.data;
  },

  // Delete profile
  async deleteProfile(profileId) {
    const api = await createApiInstance();
    const response = await api.delete(`/profiles/${profileId}`);
    return response.data;
  },

  // Duplicate profile
  async duplicateProfile(profileId, newName, regenerateFingerprint = false) {
    const api = await createApiInstance();
    const response = await api.post(`/profiles/${profileId}/duplicate`, {
      new_name: newName,
      regenerate_fingerprint: regenerateFingerprint
    });
    return response.data;
  },

  // Test profile
  async testProfile(profileId) {
    const api = await createApiInstance();
    const response = await api.post(`/profiles/${profileId}/test`);
    return response.data;
  },

  // Get profile templates
  async getTemplates() {
    const api = await createApiInstance();
    const response = await api.get('/profiles/templates/list');
    return response.data;
  },

  // Create from template
  async createFromTemplate(templateName, profileName, proxyId = null) {
    const api = await createApiInstance();
    const response = await api.post(`/profiles/templates/${templateName}`, {
      profile_name: profileName,
      proxy_id: proxyId
    });
    return response.data;
  }
};

// Proxy API
export const proxyAPI = {
  // Get all proxies
  async getProxies(params = {}) {
    const api = await createApiInstance();
    const response = await api.get('/proxies/', { params });
    return response.data;
  },

  // Get proxy by ID
  async getProxy(proxyId) {
    const api = await createApiInstance();
    const response = await api.get(`/proxies/${proxyId}`);
    return response.data;
  },

  // Create new proxy
  async createProxy(proxyData) {
    const api = await createApiInstance();
    const response = await api.post('/proxies/', proxyData);
    return response.data;
  },

  // Update proxy
  async updateProxy(proxyId, updates) {
    const api = await createApiInstance();
    const response = await api.put(`/proxies/${proxyId}`, updates);
    return response.data;
  },

  // Delete proxy
  async deleteProxy(proxyId) {
    const api = await createApiInstance();
    const response = await api.delete(`/proxies/${proxyId}`);
    return response.data;
  },

  // Validate proxy
  async validateProxy(proxyId) {
    const api = await createApiInstance();
    const response = await api.post(`/proxies/${proxyId}/validate`);
    return response.data;
  },

  // Test proxy with URL
  async testProxy(proxyId, testUrl = 'https://httpbin.org/ip') {
    const api = await createApiInstance();
    const response = await api.post(`/proxies/${proxyId}/test`, { test_url: testUrl });
    return response.data;
  },

  // Get proxy statistics
  async getStatistics() {
    const api = await createApiInstance();
    const response = await api.get('/proxies/statistics');
    return response.data;
  },

  // Validate all proxies
  async validateAll() {
    const api = await createApiInstance();
    const response = await api.post('/proxies/validate-all');
    return response.data;
  },

  // Import proxies
  async importProxies(proxies, validateOnImport = false) {
    const api = await createApiInstance();
    const response = await api.post('/proxies/import', {
      proxies,
      validate_on_import: validateOnImport
    });
    return response.data;
  }
};

// TikTok Account API
export const accountAPI = {
  // Get all accounts
  async getAccounts(params = {}) {
    const api = await createApiInstance();
    const response = await api.get('/accounts/', { params });
    return response.data;
  },

  // Get account by ID
  async getAccount(accountId) {
    const api = await createApiInstance();
    const response = await api.get(`/accounts/${accountId}`);
    return response.data;
  },

  // Create new account
  async createAccount(accountData) {
    const api = await createApiInstance();
    const response = await api.post('/accounts/', accountData);
    return response.data;
  },

  // Update account
  async updateAccount(accountId, updates) {
    const api = await createApiInstance();
    const response = await api.put(`/accounts/${accountId}`, updates);
    return response.data;
  },

  // Delete account
  async deleteAccount(accountId) {
    const api = await createApiInstance();
    const response = await api.delete(`/accounts/${accountId}`);
    return response.data;
  },

  // Login to TikTok
  async login(accountId) {
    const api = await createApiInstance();
    const response = await api.post(`/accounts/${accountId}/login`);
    return response.data;
  },

  // Complete login
  async completeLogin(accountId) {
    const api = await createApiInstance();
    const response = await api.post(`/accounts/${accountId}/complete-login`);
    return response.data;
  },

  // Check login status
  async checkLoginStatus(accountId) {
    const api = await createApiInstance();
    const response = await api.get(`/accounts/${accountId}/login-status`);
    return response.data;
  }
};

// Task API
export const taskAPI = {
  // Get all tasks
  async getTasks(params = {}) {
    const api = await createApiInstance();
    const response = await api.get('/tasks/', { params });
    return response.data;
  },

  // Get task by ID
  async getTask(taskId) {
    const api = await createApiInstance();
    const response = await api.get(`/tasks/${taskId}`);
    return response.data;
  },

  // Create new task
  async createTask(taskData) {
    const api = await createApiInstance();
    const response = await api.post('/tasks/', taskData);
    return response.data;
  },

  // Start task
  async startTask(taskId) {
    const api = await createApiInstance();
    const response = await api.post(`/tasks/${taskId}/start`);
    return response.data;
  },

  // Pause task
  async pauseTask(taskId) {
    const api = await createApiInstance();
    const response = await api.post(`/tasks/${taskId}/pause`);
    return response.data;
  },

  // Stop task
  async stopTask(taskId) {
    const api = await createApiInstance();
    const response = await api.post(`/tasks/${taskId}/stop`);
    return response.data;
  },

  // Delete task
  async deleteTask(taskId) {
    const api = await createApiInstance();
    const response = await api.delete(`/tasks/${taskId}`);
    return response.data;
  }
};

// System API
export const systemAPI = {
  // Get system status
  async getStatus() {
    const api = await createApiInstance();
    const response = await api.get('/system/status');
    return response.data;
  },

  // Get system logs
  async getLogs(params = {}) {
    const api = await createApiInstance();
    const response = await api.get('/system/logs', { params });
    return response.data;
  },

  // Clear logs
  async clearLogs() {
    const api = await createApiInstance();
    const response = await api.delete('/system/logs');
    return response.data;
  },

  // Export logs
  async exportLogs(format = 'txt') {
    const api = await createApiInstance();
    const response = await api.get(`/system/logs/export?format=${format}`, {
      responseType: 'blob'
    });
    return response.data;
  }
};

// Antidetect API
export const antidetectAPI = {
  // Test antidetect capabilities
  async testProfile(profileId, testSites = null, includeTikTok = true, headless = false) {
    const api = await createApiInstance();
    const response = await api.post('/antidetect/test', {
      profile_id: profileId,
      test_sites: testSites,
      include_tiktok_test: includeTikTok,
      headless: headless
    });
    return response.data;
  },

  // Get available test sites
  async getTestSites() {
    const api = await createApiInstance();
    const response = await api.get('/antidetect/test-sites');
    return response.data;
  },

  // Optimize profile for antidetect
  async optimizeProfile(profileId) {
    const api = await createApiInstance();
    const response = await api.post(`/antidetect/optimize/${profileId}`);
    return response.data;
  },

  // Generate fingerprint
  async generateFingerprint(osType = 'windows', browserType = 'firefox', location = null) {
    const api = await createApiInstance();
    const response = await api.get('/antidetect/fingerprint/generate', {
      params: { os_type: osType, browser_type: browserType, location }
    });
    return response.data;
  },

  // Validate TikTok access
  async validateTikTokAccess(profileId) {
    const api = await createApiInstance();
    const response = await api.post('/antidetect/validate/tiktok', { profile_id: profileId });
    return response.data;
  },

  // Get detection statistics
  async getDetectionStats() {
    const api = await createApiInstance();
    const response = await api.get('/antidetect/stats/detection');
    return response.data;
  },

  // Run benchmark
  async runBenchmark() {
    const api = await createApiInstance();
    const response = await api.post('/antidetect/benchmark');
    return response.data;
  },

  // Get recommendations
  async getRecommendations(profileId) {
    const api = await createApiInstance();
    const response = await api.get(`/antidetect/recommendations/${profileId}`);
    return response.data;
  },

  // Enhance stealth configuration
  async enhanceStealth(profileId) {
    const api = await createApiInstance();
    const response = await api.post(`/antidetect/stealth/enhance/${profileId}`);
    return response.data;
  }
};

// Error handler
export const handleApiError = (error) => {
  if (error.response) {
    // Server responded with error status
    const { status, data } = error.response;
    return {
      status,
      message: data.detail || data.message || 'Server error',
      data: data
    };
  } else if (error.request) {
    // Request was made but no response received
    return {
      status: 0,
      message: 'Network error - unable to connect to server',
      data: null
    };
  } else {
    // Something else happened
    return {
      status: -1,
      message: error.message || 'Unknown error',
      data: null
    };
  }
};

export default {
  profileAPI,
  proxyAPI,
  accountAPI,
  taskAPI,
  systemAPI,
  antidetectAPI,
  handleApiError
};
