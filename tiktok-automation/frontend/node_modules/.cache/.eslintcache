[{"/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/index.js": "1", "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/App.js": "2", "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/pages/Dashboard.jsx": "3", "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Layout/Layout.jsx": "4", "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Layout/Sidebar.jsx": "5", "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Dashboard/RecentActivity.jsx": "6", "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Dashboard/StatsCard.jsx": "7", "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Dashboard/TasksOverview.jsx": "8", "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Dashboard/SystemStatus.jsx": "9", "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Layout/Header.jsx": "10", "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/hooks/useWebSocket.js": "11", "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Dashboard/ProfileTable.jsx": "12", "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Dashboard/LogMonitor.jsx": "13", "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Dashboard/ProfileForm.jsx": "14", "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/services/profileService.js": "15", "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/services/api.js": "16", "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Dashboard/AntidetectTester.jsx": "17"}, {"size": 254, "mtime": 1751527285412, "results": "18", "hashOfConfig": "19"}, {"size": 536, "mtime": 1751534968738, "results": "20", "hashOfConfig": "19"}, {"size": 12259, "mtime": 1751534918111, "results": "21", "hashOfConfig": "19"}, {"size": 4240, "mtime": 1751514858865, "results": "22", "hashOfConfig": "19"}, {"size": 5692, "mtime": 1751527636477, "results": "23", "hashOfConfig": "19"}, {"size": 8238, "mtime": 1751534883039, "results": "24", "hashOfConfig": "19"}, {"size": 2412, "mtime": 1751514933198, "results": "25", "hashOfConfig": "19"}, {"size": 7496, "mtime": 1751534928586, "results": "26", "hashOfConfig": "19"}, {"size": 7652, "mtime": 1751514997454, "results": "27", "hashOfConfig": "19"}, {"size": 9110, "mtime": 1751514837511, "results": "28", "hashOfConfig": "19"}, {"size": 5786, "mtime": 1751514885561, "results": "29", "hashOfConfig": "19"}, {"size": 7179, "mtime": 1751531599487, "results": "30", "hashOfConfig": "19"}, {"size": 7344, "mtime": 1751530469601, "results": "31", "hashOfConfig": "19"}, {"size": 10861, "mtime": 1751531232245, "results": "32", "hashOfConfig": "19"}, {"size": 11872, "mtime": 1751535017530, "results": "33", "hashOfConfig": "19"}, {"size": 11486, "mtime": 1751531631260, "results": "34", "hashOfConfig": "19"}, {"size": 10510, "mtime": 1751531681667, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "oyi8yw", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/index.js", [], [], "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/App.js", [], [], "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/pages/Dashboard.jsx", [], [], "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Layout/Layout.jsx", [], [], "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Layout/Sidebar.jsx", ["87"], [], "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Dashboard/RecentActivity.jsx", [], [], "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Dashboard/StatsCard.jsx", [], [], "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Dashboard/TasksOverview.jsx", [], [], "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Dashboard/SystemStatus.jsx", [], [], "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Layout/Header.jsx", [], [], "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/hooks/useWebSocket.js", ["88"], [], "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Dashboard/ProfileTable.jsx", [], [], "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Dashboard/LogMonitor.jsx", [], [], "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Dashboard/ProfileForm.jsx", [], [], "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/services/profileService.js", [], [], "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/services/api.js", ["89"], [], "/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Dashboard/AntidetectTester.jsx", [], [], {"ruleId": "90", "severity": 1, "message": "91", "line": 5, "column": 17, "nodeType": "92", "messageId": "93", "endLine": 5, "endColumn": 25}, {"ruleId": "94", "severity": 1, "message": "95", "line": 87, "column": 6, "nodeType": "96", "endLine": 87, "endColumn": 23, "suggestions": "97"}, {"ruleId": "98", "severity": 1, "message": "99", "line": 419, "column": 1, "nodeType": "100", "endLine": 427, "endColumn": 3}, "no-unused-vars", "'useState' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'handleMessage'. Either include it or remove the dependency array.", "ArrayExpression", ["101"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", {"desc": "102", "fix": "103"}, "Update the dependencies array to be: [getWebSocketUrl, handleMessage]", {"range": "104", "text": "105"}, [2789, 2806], "[getWebSocketUrl, handleMessage]"]