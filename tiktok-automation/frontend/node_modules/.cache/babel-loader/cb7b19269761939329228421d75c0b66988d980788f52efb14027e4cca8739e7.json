{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/pages/Dashboard.jsx\",\n  _s = $RefreshSig$();\n/**\n * TikTok Automation Dashboard - Main management interface\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { FiPlus, FiPlay, FiPause, FiStopCircle } from 'react-icons/fi';\n\n// Components\nimport ProfileTable from '../components/Dashboard/ProfileTable';\nimport ProfileForm from '../components/Dashboard/ProfileForm';\nimport LogMonitor from '../components/Dashboard/LogMonitor';\n\n// Services\nimport profileService from '../services/profileService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  // State management\n  const [profiles, setProfiles] = useState([]);\n  const [showProfileForm, setShowProfileForm] = useState(false);\n  const [selectedProfiles, setSelectedProfiles] = useState([]);\n  const [automationSettings, setAutomationSettings] = useState({\n    targetProfile: '',\n    videosToWatch: 3,\n    watchTimeRange: '2000-5000',\n    followLimit: 10,\n    followDelay: '3000-8000'\n  });\n  const [logs, setLogs] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Load data from backend\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        setLoading(true);\n\n        // Load profiles from backend\n        await profileService.loadProfiles();\n        setProfiles(profileService.getProfiles());\n\n        // Mock logs for now - TODO: Load from backend\n        const mockLogs = [{\n          id: 1,\n          time: '09:45:30',\n          level: 'INFO',\n          message: 'System started successfully'\n        }, {\n          id: 2,\n          time: '09:45:31',\n          level: 'INFO',\n          message: 'Backend connection established'\n        }, {\n          id: 3,\n          time: '09:45:32',\n          level: 'INFO',\n          message: 'Profile service initialized'\n        }, {\n          id: 4,\n          time: '09:45:33',\n          level: 'INFO',\n          message: 'Ready for automation tasks'\n        }];\n        setLogs(mockLogs);\n      } catch (error) {\n        console.error('Failed to load data:', error);\n        // Add error log\n        setLogs(prev => [...prev, {\n          id: Date.now(),\n          time: new Date().toLocaleTimeString(),\n          level: 'ERROR',\n          message: `Failed to load profiles: ${error.message}`\n        }]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadData();\n\n    // Set up profile service listeners\n    const handleProfileEvent = (event, data) => {\n      const timestamp = new Date().toLocaleTimeString();\n      let logMessage = '';\n      switch (event) {\n        case 'profiles_loaded':\n          setProfiles(data);\n          logMessage = `Loaded ${data.length} profiles from backend`;\n          break;\n        case 'profile_created':\n          setProfiles(profileService.getProfiles());\n          logMessage = `Profile \"${data.username}\" created successfully`;\n          break;\n        case 'profile_updated':\n          setProfiles(profileService.getProfiles());\n          logMessage = `Profile \"${data.username}\" updated`;\n          break;\n        case 'profile_deleted':\n          setProfiles(profileService.getProfiles());\n          logMessage = `Profile deleted (ID: ${data})`;\n          break;\n        case 'login_started':\n          logMessage = `Login started for profile ID: ${data}`;\n          break;\n        case 'login_completed':\n          logMessage = `Login completed for profile ID: ${data}`;\n          break;\n        case 'automation_started':\n          logMessage = `Automation started for profile ID: ${data}`;\n          break;\n        case 'automation_paused':\n          logMessage = `Automation paused for profile ID: ${data}`;\n          break;\n        case 'automation_stopped':\n          logMessage = `Automation stopped for profile ID: ${data}`;\n          break;\n        default:\n          return;\n      }\n      if (logMessage) {\n        setLogs(prev => [...prev, {\n          id: Date.now(),\n          time: timestamp,\n          level: 'INFO',\n          message: logMessage\n        }]);\n      }\n    };\n    profileService.addListener(handleProfileEvent);\n\n    // Cleanup\n    return () => {\n      profileService.removeListener(handleProfileEvent);\n    };\n  }, []);\n\n  // Handle profile actions\n  const handleProfileAction = (profileId, action) => {\n    console.log(`Action ${action} for profile ${profileId}`);\n    // TODO: Implement actual API calls\n\n    // Update profile status based on action\n    setProfiles(prev => prev.map(profile => {\n      if (profile.id === profileId) {\n        switch (action) {\n          case 'login':\n            return {\n              ...profile,\n              status: 'ĐANG ĐĂNG NHẬP',\n              currentAction: 'Đang đăng nhập...'\n            };\n          case 'complete':\n            return {\n              ...profile,\n              status: 'SẴN SÀNG',\n              currentAction: 'Sẵn sàng chạy',\n              actions: ['start', 'stop']\n            };\n          case 'start':\n            return {\n              ...profile,\n              status: 'ĐANG NHẬP HOẠT ĐỘ',\n              currentAction: 'Đang tương tác',\n              actions: ['pause', 'stop']\n            };\n          case 'pause':\n            return {\n              ...profile,\n              status: 'TẠM DỪNG',\n              currentAction: 'Đã tạm dừng',\n              actions: ['start', 'stop']\n            };\n          case 'stop':\n            return {\n              ...profile,\n              status: 'SẴN SÀNG',\n              currentAction: 'Đã dừng',\n              actions: ['start']\n            };\n          default:\n            return profile;\n        }\n      }\n      return profile;\n    }));\n  };\n  const handleProfileSelect = (profileId, isSelected) => {\n    setSelectedProfiles(prev => {\n      if (isSelected) {\n        return [...prev, profileId];\n      } else {\n        return prev.filter(id => id !== profileId);\n      }\n    });\n  };\n  const handleSelectAll = isSelected => {\n    if (isSelected) {\n      setSelectedProfiles(profiles.map(p => p.id));\n    } else {\n      setSelectedProfiles([]);\n    }\n  };\n  const handleBulkAction = action => {\n    console.log(`Bulk action ${action} for profiles:`, selectedProfiles);\n    selectedProfiles.forEach(profileId => {\n      handleProfileAction(profileId, action);\n    });\n  };\n  const handleCreateProfile = profileData => {\n    console.log('Creating profile:', profileData);\n\n    // Add new profile to list\n    const newProfile = {\n      id: profiles.length + 1,\n      stt: profiles.length + 1,\n      username: profileData.profileName,\n      proxy: profileData.proxyType === 'no-proxy' ? 'Local Network' : `${profileData.host}:${profileData.port}`,\n      status: 'CHƯA ĐĂNG NHẬP',\n      followersFollowed: 0,\n      followersToday: 0,\n      currentAction: 'Chờ đăng nhập',\n      actions: ['login']\n    };\n    setProfiles(prev => [...prev, newProfile]);\n    setShowProfileForm(false);\n\n    // Add log entry\n    const newLog = {\n      id: logs.length + 1,\n      time: new Date().toLocaleTimeString(),\n      level: 'INFO',\n      message: `Profile \"${profileData.profileName}\" đã được tạo thành công`\n    };\n    setLogs(prev => [...prev, newLog]);\n  };\n  const handleClearLogs = () => {\n    setLogs([]);\n  };\n  const handleExportLogs = () => {\n    console.log('Exporting logs...');\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-purple-600 via-purple-700 to-purple-800\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/10 backdrop-blur-sm border-b border-white/20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-6 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-white\",\n            children: \"TikTok Automation Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 bg-green-500 px-3 py-1 rounded-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-2 h-2 bg-white rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white text-sm font-medium\",\n                children: \"Online\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-3 space-y-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 border-b border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-lg font-semibold text-gray-900\",\n                  children: \"Qu\\u1EA3n l\\xFD T\\xE0i kho\\u1EA3n (3)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setShowProfileForm(true),\n                    className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(FiPlus, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 258,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"T\\u1EA1o m\\u1EDBi\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 259,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 254,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleBulkAction('start'),\n                    className: \"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(FiPlay, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 265,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Ch\\u1EA1y t\\u1EA5t c\\u1EA3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 266,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleBulkAction('stop'),\n                    className: \"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(FiStopCircle, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 272,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"D\\u1EEBng t\\u1EA5t c\\u1EA3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 273,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 268,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ProfileTable, {\n              profiles: profiles,\n              selectedProfiles: selectedProfiles,\n              onProfileSelect: handleProfileSelect,\n              onProfileAction: handleProfileAction,\n              onSelectAll: handleSelectAll\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-sm p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"\\u0110i\\u1EC1u khi\\u1EC3n & C\\xE0i \\u0111\\u1EB7t\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Ng\\xE0y \\u0111\\u1ED5i theo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"px-3 py-1 bg-blue-600 text-white rounded text-sm\",\n                    children: \"H\\xF4m nay\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 300,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm\",\n                    children: \"H\\xF4m qua\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm\",\n                    children: \"Tu\\u1EA7n n\\xE0y\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: [\"\\u0110i\\u1EC1u khi\\u1EC3n (\", selectedProfiles.length, \" t\\xE0i kho\\u1EA3n \\u0111ang ch\\u1ECDn)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleBulkAction('start'),\n                    disabled: selectedProfiles.length === 0,\n                    className: \"px-3 py-1 bg-green-600 text-white rounded text-sm flex items-center space-x-1 disabled:opacity-50 disabled:cursor-not-allowed\",\n                    children: [/*#__PURE__*/_jsxDEV(FiPlay, {\n                      className: \"w-3 h-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 316,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"B\\u1EAFt \\u0111\\u1EA7u\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 317,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 311,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleBulkAction('pause'),\n                    disabled: selectedProfiles.length === 0,\n                    className: \"px-3 py-1 bg-yellow-600 text-white rounded text-sm flex items-center space-x-1 disabled:opacity-50 disabled:cursor-not-allowed\",\n                    children: [/*#__PURE__*/_jsxDEV(FiPause, {\n                      className: \"w-3 h-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 324,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"T\\u1EA1m d\\u1EEBng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 325,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleBulkAction('stop'),\n                    disabled: selectedProfiles.length === 0,\n                    className: \"px-3 py-1 bg-red-600 text-white rounded text-sm flex items-center space-x-1 disabled:opacity-50 disabled:cursor-not-allowed\",\n                    children: [/*#__PURE__*/_jsxDEV(FiStopCircle, {\n                      className: \"w-3 h-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 332,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"D\\u1EEBng h\\u1EB3n\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 333,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 327,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"C\\xE0i \\u0111\\u1EB7t k\\u1ECBch b\\u1EA3n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-xs text-gray-600 mb-1\",\n                      children: \"Link profile \\u0111\\u1ED1i th\\u1EE7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 342,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      placeholder: \"https://www.tiktok.com/@username\",\n                      className: \"w-full px-3 py-2 border border-gray-300 rounded text-sm\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 343,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 341,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-2 gap-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-xs text-gray-600 mb-1\",\n                        children: \"S\\u1ED1 video xem\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 352,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"number\",\n                        defaultValue: \"3\",\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded text-sm\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 353,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 351,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-xs text-gray-600 mb-1\",\n                        children: \"Th\\u1EDDi gian xem (gi\\xE2y)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 356,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        defaultValue: \"30\",\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded text-sm\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 357,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 355,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 350,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-2 gap-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-xs text-gray-600 mb-1\",\n                        children: \"Follow t\\u1ED1i \\u0111a/ng\\xE0y\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 363,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"number\",\n                        defaultValue: \"10\",\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded text-sm\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 364,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 362,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-xs text-gray-600 mb-1\",\n                        children: \"Kho\\u1EA3ng c\\xE1ch gi\\u1EEFa phi\\xEAn (gi\\xE2y)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 367,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        defaultValue: \"3600\",\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded text-sm\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 368,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 366,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 361,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 transition-colors\",\n                    children: \"C\\u1EADp nh\\u1EADt c\\xE0i \\u0111\\u1EB7t\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(LogMonitor, {\n            logs: logs,\n            onClearLogs: handleClearLogs,\n            onExportLogs: handleExportLogs\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ProfileForm, {\n      isOpen: showProfileForm,\n      onClose: () => setShowProfileForm(false),\n      onSubmit: handleCreateProfile\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 391,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 228,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"nmTYStw3nBMO7fsxrNUml+M5nsY=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FiPlus", "FiPlay", "FiPause", "FiStopCircle", "ProfileTable", "ProfileForm", "LogMonitor", "profileService", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "profiles", "setProfiles", "showProfileForm", "setShowProfileForm", "selected<PERSON><PERSON><PERSON><PERSON>", "setSelectedProfiles", "automationSettings", "setAutomationSettings", "targetProfile", "videosToWatch", "watchTimeRange", "followLimit", "<PERSON><PERSON><PERSON><PERSON>", "logs", "setLogs", "loading", "setLoading", "loadData", "loadProfiles", "getProfiles", "mockLogs", "id", "time", "level", "message", "error", "console", "prev", "Date", "now", "toLocaleTimeString", "handleProfileEvent", "event", "data", "timestamp", "logMessage", "length", "username", "addListener", "removeListener", "handleProfileAction", "profileId", "action", "log", "map", "profile", "status", "currentAction", "actions", "handleProfileSelect", "isSelected", "filter", "handleSelectAll", "p", "handleBulkAction", "for<PERSON>ach", "handleCreateProfile", "profileData", "newProfile", "stt", "profileName", "proxy", "proxyType", "host", "port", "followersFollowed", "followers<PERSON>oday", "newLog", "handleClearLogs", "handleExportLogs", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onProfileSelect", "onProfileAction", "onSelectAll", "disabled", "type", "placeholder", "defaultValue", "onClearLogs", "onExportLogs", "isOpen", "onClose", "onSubmit", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/pages/Dashboard.jsx"], "sourcesContent": ["/**\n * TikTok Automation Dashboard - Main management interface\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  FiPlus,\n  FiPlay,\n  FiPause,\n  FiStopCircle\n} from 'react-icons/fi';\n\n// Components\nimport ProfileTable from '../components/Dashboard/ProfileTable';\nimport ProfileForm from '../components/Dashboard/ProfileForm';\nimport LogMonitor from '../components/Dashboard/LogMonitor';\n\n// Services\nimport profileService from '../services/profileService';\n\nconst Dashboard = () => {\n  // State management\n  const [profiles, setProfiles] = useState([]);\n  const [showProfileForm, setShowProfileForm] = useState(false);\n  const [selectedProfiles, setSelectedProfiles] = useState([]);\n  const [automationSettings, setAutomationSettings] = useState({\n    targetProfile: '',\n    videosToWatch: 3,\n    watchTimeRange: '2000-5000',\n    followLimit: 10,\n    followDelay: '3000-8000'\n  });\n  const [logs, setLogs] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Load data from backend\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        setLoading(true);\n\n        // Load profiles from backend\n        await profileService.loadProfiles();\n        setProfiles(profileService.getProfiles());\n\n        // Mock logs for now - TODO: Load from backend\n        const mockLogs = [\n          { id: 1, time: '09:45:30', level: 'INFO', message: 'System started successfully' },\n          { id: 2, time: '09:45:31', level: 'INFO', message: 'Backend connection established' },\n          { id: 3, time: '09:45:32', level: 'INFO', message: 'Profile service initialized' },\n          { id: 4, time: '09:45:33', level: 'INFO', message: 'Ready for automation tasks' }\n        ];\n        setLogs(mockLogs);\n\n      } catch (error) {\n        console.error('Failed to load data:', error);\n        // Add error log\n        setLogs(prev => [...prev, {\n          id: Date.now(),\n          time: new Date().toLocaleTimeString(),\n          level: 'ERROR',\n          message: `Failed to load profiles: ${error.message}`\n        }]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadData();\n\n    // Set up profile service listeners\n    const handleProfileEvent = (event, data) => {\n      const timestamp = new Date().toLocaleTimeString();\n      let logMessage = '';\n\n      switch (event) {\n        case 'profiles_loaded':\n          setProfiles(data);\n          logMessage = `Loaded ${data.length} profiles from backend`;\n          break;\n        case 'profile_created':\n          setProfiles(profileService.getProfiles());\n          logMessage = `Profile \"${data.username}\" created successfully`;\n          break;\n        case 'profile_updated':\n          setProfiles(profileService.getProfiles());\n          logMessage = `Profile \"${data.username}\" updated`;\n          break;\n        case 'profile_deleted':\n          setProfiles(profileService.getProfiles());\n          logMessage = `Profile deleted (ID: ${data})`;\n          break;\n        case 'login_started':\n          logMessage = `Login started for profile ID: ${data}`;\n          break;\n        case 'login_completed':\n          logMessage = `Login completed for profile ID: ${data}`;\n          break;\n        case 'automation_started':\n          logMessage = `Automation started for profile ID: ${data}`;\n          break;\n        case 'automation_paused':\n          logMessage = `Automation paused for profile ID: ${data}`;\n          break;\n        case 'automation_stopped':\n          logMessage = `Automation stopped for profile ID: ${data}`;\n          break;\n        default:\n          return;\n      }\n\n      if (logMessage) {\n        setLogs(prev => [...prev, {\n          id: Date.now(),\n          time: timestamp,\n          level: 'INFO',\n          message: logMessage\n        }]);\n      }\n    };\n\n    profileService.addListener(handleProfileEvent);\n\n    // Cleanup\n    return () => {\n      profileService.removeListener(handleProfileEvent);\n    };\n  }, []);\n\n  // Handle profile actions\n  const handleProfileAction = (profileId, action) => {\n    console.log(`Action ${action} for profile ${profileId}`);\n    // TODO: Implement actual API calls\n\n    // Update profile status based on action\n    setProfiles(prev => prev.map(profile => {\n      if (profile.id === profileId) {\n        switch (action) {\n          case 'login':\n            return { ...profile, status: 'ĐANG ĐĂNG NHẬP', currentAction: 'Đang đăng nhập...' };\n          case 'complete':\n            return { ...profile, status: 'SẴN SÀNG', currentAction: 'Sẵn sàng chạy', actions: ['start', 'stop'] };\n          case 'start':\n            return { ...profile, status: 'ĐANG NHẬP HOẠT ĐỘ', currentAction: 'Đang tương tác', actions: ['pause', 'stop'] };\n          case 'pause':\n            return { ...profile, status: 'TẠM DỪNG', currentAction: 'Đã tạm dừng', actions: ['start', 'stop'] };\n          case 'stop':\n            return { ...profile, status: 'SẴN SÀNG', currentAction: 'Đã dừng', actions: ['start'] };\n          default:\n            return profile;\n        }\n      }\n      return profile;\n    }));\n  };\n\n  const handleProfileSelect = (profileId, isSelected) => {\n    setSelectedProfiles(prev => {\n      if (isSelected) {\n        return [...prev, profileId];\n      } else {\n        return prev.filter(id => id !== profileId);\n      }\n    });\n  };\n\n  const handleSelectAll = (isSelected) => {\n    if (isSelected) {\n      setSelectedProfiles(profiles.map(p => p.id));\n    } else {\n      setSelectedProfiles([]);\n    }\n  };\n\n  const handleBulkAction = (action) => {\n    console.log(`Bulk action ${action} for profiles:`, selectedProfiles);\n    selectedProfiles.forEach(profileId => {\n      handleProfileAction(profileId, action);\n    });\n  };\n\n  const handleCreateProfile = (profileData) => {\n    console.log('Creating profile:', profileData);\n\n    // Add new profile to list\n    const newProfile = {\n      id: profiles.length + 1,\n      stt: profiles.length + 1,\n      username: profileData.profileName,\n      proxy: profileData.proxyType === 'no-proxy' ? 'Local Network' : `${profileData.host}:${profileData.port}`,\n      status: 'CHƯA ĐĂNG NHẬP',\n      followersFollowed: 0,\n      followersToday: 0,\n      currentAction: 'Chờ đăng nhập',\n      actions: ['login']\n    };\n\n    setProfiles(prev => [...prev, newProfile]);\n    setShowProfileForm(false);\n\n    // Add log entry\n    const newLog = {\n      id: logs.length + 1,\n      time: new Date().toLocaleTimeString(),\n      level: 'INFO',\n      message: `Profile \"${profileData.profileName}\" đã được tạo thành công`\n    };\n    setLogs(prev => [...prev, newLog]);\n  };\n\n  const handleClearLogs = () => {\n    setLogs([]);\n  };\n\n  const handleExportLogs = () => {\n    console.log('Exporting logs...');\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-purple-600 via-purple-700 to-purple-800\">\n      {/* Header */}\n      <div className=\"bg-white/10 backdrop-blur-sm border-b border-white/20\">\n        <div className=\"px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <h1 className=\"text-2xl font-bold text-white\">TikTok Automation Dashboard</h1>\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"flex items-center space-x-2 bg-green-500 px-3 py-1 rounded-full\">\n                <div className=\"w-2 h-2 bg-white rounded-full\"></div>\n                <span className=\"text-white text-sm font-medium\">Online</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"p-6\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6\">\n          {/* Left Panel - Profile Management */}\n          <div className=\"lg:col-span-3 space-y-6\">\n            {/* Profile Controls */}\n            <div className=\"bg-white rounded-lg shadow-sm\">\n              <div className=\"p-4 border-b border-gray-200\">\n                <div className=\"flex items-center justify-between\">\n                  <h2 className=\"text-lg font-semibold text-gray-900\">Quản lý Tài khoản (3)</h2>\n                  <div className=\"flex items-center space-x-2\">\n                    <button\n                      onClick={() => setShowProfileForm(true)}\n                      className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2\"\n                    >\n                      <FiPlus className=\"w-4 h-4\" />\n                      <span>Tạo mới</span>\n                    </button>\n                    <button\n                      onClick={() => handleBulkAction('start')}\n                      className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2\"\n                    >\n                      <FiPlay className=\"w-4 h-4\" />\n                      <span>Chạy tất cả</span>\n                    </button>\n                    <button\n                      onClick={() => handleBulkAction('stop')}\n                      className=\"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2\"\n                    >\n                      <FiStopCircle className=\"w-4 h-4\" />\n                      <span>Dừng tất cả</span>\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              {/* Profile Table */}\n              <ProfileTable\n                profiles={profiles}\n                selectedProfiles={selectedProfiles}\n                onProfileSelect={handleProfileSelect}\n                onProfileAction={handleProfileAction}\n                onSelectAll={handleSelectAll}\n              />\n            </div>\n          </div>\n\n          {/* Right Panel - Settings and Logs */}\n          <div className=\"space-y-6\">\n            {/* Automation Settings */}\n            <div className=\"bg-white rounded-lg shadow-sm p-4\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Điều khiển & Cài đặt</h3>\n\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Ngày đổi theo</label>\n                  <div className=\"flex space-x-2\">\n                    <button className=\"px-3 py-1 bg-blue-600 text-white rounded text-sm\">Hôm nay</button>\n                    <button className=\"px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm\">Hôm qua</button>\n                    <button className=\"px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm\">Tuần này</button>\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Điều khiển ({selectedProfiles.length} tài khoản đang chọn)\n                  </label>\n                  <div className=\"flex space-x-2\">\n                    <button\n                      onClick={() => handleBulkAction('start')}\n                      disabled={selectedProfiles.length === 0}\n                      className=\"px-3 py-1 bg-green-600 text-white rounded text-sm flex items-center space-x-1 disabled:opacity-50 disabled:cursor-not-allowed\"\n                    >\n                      <FiPlay className=\"w-3 h-3\" />\n                      <span>Bắt đầu</span>\n                    </button>\n                    <button\n                      onClick={() => handleBulkAction('pause')}\n                      disabled={selectedProfiles.length === 0}\n                      className=\"px-3 py-1 bg-yellow-600 text-white rounded text-sm flex items-center space-x-1 disabled:opacity-50 disabled:cursor-not-allowed\"\n                    >\n                      <FiPause className=\"w-3 h-3\" />\n                      <span>Tạm dừng</span>\n                    </button>\n                    <button\n                      onClick={() => handleBulkAction('stop')}\n                      disabled={selectedProfiles.length === 0}\n                      className=\"px-3 py-1 bg-red-600 text-white rounded text-sm flex items-center space-x-1 disabled:opacity-50 disabled:cursor-not-allowed\"\n                    >\n                      <FiStopCircle className=\"w-3 h-3\" />\n                      <span>Dừng hẳn</span>\n                    </button>\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Cài đặt kịch bản</label>\n                  <div className=\"space-y-3\">\n                    <div>\n                      <label className=\"block text-xs text-gray-600 mb-1\">Link profile đối thủ</label>\n                      <input\n                        type=\"text\"\n                        placeholder=\"https://www.tiktok.com/@username\"\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded text-sm\"\n                      />\n                    </div>\n\n                    <div className=\"grid grid-cols-2 gap-2\">\n                      <div>\n                        <label className=\"block text-xs text-gray-600 mb-1\">Số video xem</label>\n                        <input type=\"number\" defaultValue=\"3\" className=\"w-full px-3 py-2 border border-gray-300 rounded text-sm\" />\n                      </div>\n                      <div>\n                        <label className=\"block text-xs text-gray-600 mb-1\">Thời gian xem (giây)</label>\n                        <input type=\"text\" defaultValue=\"30\" className=\"w-full px-3 py-2 border border-gray-300 rounded text-sm\" />\n                      </div>\n                    </div>\n\n                    <div className=\"grid grid-cols-2 gap-2\">\n                      <div>\n                        <label className=\"block text-xs text-gray-600 mb-1\">Follow tối đa/ngày</label>\n                        <input type=\"number\" defaultValue=\"10\" className=\"w-full px-3 py-2 border border-gray-300 rounded text-sm\" />\n                      </div>\n                      <div>\n                        <label className=\"block text-xs text-gray-600 mb-1\">Khoảng cách giữa phiên (giây)</label>\n                        <input type=\"text\" defaultValue=\"3600\" className=\"w-full px-3 py-2 border border-gray-300 rounded text-sm\" />\n                      </div>\n                    </div>\n\n                    <button className=\"w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 transition-colors\">\n                      Cập nhật cài đặt\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Log Monitor */}\n            <LogMonitor\n              logs={logs}\n              onClearLogs={handleClearLogs}\n              onExportLogs={handleExportLogs}\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Profile Form Modal */}\n      <ProfileForm\n        isOpen={showProfileForm}\n        onClose={() => setShowProfileForm(false)}\n        onSubmit={handleCreateProfile}\n      />\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,YAAY,QACP,gBAAgB;;AAEvB;AACA,OAAOC,YAAY,MAAM,sCAAsC;AAC/D,OAAOC,WAAW,MAAM,qCAAqC;AAC7D,OAAOC,UAAU,MAAM,oCAAoC;;AAE3D;AACA,OAAOC,cAAc,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACkB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACoB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrB,QAAQ,CAAC;IAC3DsB,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE,WAAW;IAC3BC,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,MAAM8B,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI;QACFD,UAAU,CAAC,IAAI,CAAC;;QAEhB;QACA,MAAMrB,cAAc,CAACuB,YAAY,CAAC,CAAC;QACnCjB,WAAW,CAACN,cAAc,CAACwB,WAAW,CAAC,CAAC,CAAC;;QAEzC;QACA,MAAMC,QAAQ,GAAG,CACf;UAAEC,EAAE,EAAE,CAAC;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE,MAAM;UAAEC,OAAO,EAAE;QAA8B,CAAC,EAClF;UAAEH,EAAE,EAAE,CAAC;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAiC,CAAC,EACrF;UAAEH,EAAE,EAAE,CAAC;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE,MAAM;UAAEC,OAAO,EAAE;QAA8B,CAAC,EAClF;UAAEH,EAAE,EAAE,CAAC;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE,MAAM;UAAEC,OAAO,EAAE;QAA6B,CAAC,CAClF;QACDV,OAAO,CAACM,QAAQ,CAAC;MAEnB,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C;QACAX,OAAO,CAACa,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UACxBN,EAAE,EAAEO,IAAI,CAACC,GAAG,CAAC,CAAC;UACdP,IAAI,EAAE,IAAIM,IAAI,CAAC,CAAC,CAACE,kBAAkB,CAAC,CAAC;UACrCP,KAAK,EAAE,OAAO;UACdC,OAAO,EAAE,4BAA4BC,KAAK,CAACD,OAAO;QACpD,CAAC,CAAC,CAAC;MACL,CAAC,SAAS;QACRR,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDC,QAAQ,CAAC,CAAC;;IAEV;IACA,MAAMc,kBAAkB,GAAGA,CAACC,KAAK,EAAEC,IAAI,KAAK;MAC1C,MAAMC,SAAS,GAAG,IAAIN,IAAI,CAAC,CAAC,CAACE,kBAAkB,CAAC,CAAC;MACjD,IAAIK,UAAU,GAAG,EAAE;MAEnB,QAAQH,KAAK;QACX,KAAK,iBAAiB;UACpB/B,WAAW,CAACgC,IAAI,CAAC;UACjBE,UAAU,GAAG,UAAUF,IAAI,CAACG,MAAM,wBAAwB;UAC1D;QACF,KAAK,iBAAiB;UACpBnC,WAAW,CAACN,cAAc,CAACwB,WAAW,CAAC,CAAC,CAAC;UACzCgB,UAAU,GAAG,YAAYF,IAAI,CAACI,QAAQ,wBAAwB;UAC9D;QACF,KAAK,iBAAiB;UACpBpC,WAAW,CAACN,cAAc,CAACwB,WAAW,CAAC,CAAC,CAAC;UACzCgB,UAAU,GAAG,YAAYF,IAAI,CAACI,QAAQ,WAAW;UACjD;QACF,KAAK,iBAAiB;UACpBpC,WAAW,CAACN,cAAc,CAACwB,WAAW,CAAC,CAAC,CAAC;UACzCgB,UAAU,GAAG,wBAAwBF,IAAI,GAAG;UAC5C;QACF,KAAK,eAAe;UAClBE,UAAU,GAAG,iCAAiCF,IAAI,EAAE;UACpD;QACF,KAAK,iBAAiB;UACpBE,UAAU,GAAG,mCAAmCF,IAAI,EAAE;UACtD;QACF,KAAK,oBAAoB;UACvBE,UAAU,GAAG,sCAAsCF,IAAI,EAAE;UACzD;QACF,KAAK,mBAAmB;UACtBE,UAAU,GAAG,qCAAqCF,IAAI,EAAE;UACxD;QACF,KAAK,oBAAoB;UACvBE,UAAU,GAAG,sCAAsCF,IAAI,EAAE;UACzD;QACF;UACE;MACJ;MAEA,IAAIE,UAAU,EAAE;QACdrB,OAAO,CAACa,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UACxBN,EAAE,EAAEO,IAAI,CAACC,GAAG,CAAC,CAAC;UACdP,IAAI,EAAEY,SAAS;UACfX,KAAK,EAAE,MAAM;UACbC,OAAO,EAAEW;QACX,CAAC,CAAC,CAAC;MACL;IACF,CAAC;IAEDxC,cAAc,CAAC2C,WAAW,CAACP,kBAAkB,CAAC;;IAE9C;IACA,OAAO,MAAM;MACXpC,cAAc,CAAC4C,cAAc,CAACR,kBAAkB,CAAC;IACnD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMS,mBAAmB,GAAGA,CAACC,SAAS,EAAEC,MAAM,KAAK;IACjDhB,OAAO,CAACiB,GAAG,CAAC,UAAUD,MAAM,gBAAgBD,SAAS,EAAE,CAAC;IACxD;;IAEA;IACAxC,WAAW,CAAC0B,IAAI,IAAIA,IAAI,CAACiB,GAAG,CAACC,OAAO,IAAI;MACtC,IAAIA,OAAO,CAACxB,EAAE,KAAKoB,SAAS,EAAE;QAC5B,QAAQC,MAAM;UACZ,KAAK,OAAO;YACV,OAAO;cAAE,GAAGG,OAAO;cAAEC,MAAM,EAAE,gBAAgB;cAAEC,aAAa,EAAE;YAAoB,CAAC;UACrF,KAAK,UAAU;YACb,OAAO;cAAE,GAAGF,OAAO;cAAEC,MAAM,EAAE,UAAU;cAAEC,aAAa,EAAE,eAAe;cAAEC,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM;YAAE,CAAC;UACvG,KAAK,OAAO;YACV,OAAO;cAAE,GAAGH,OAAO;cAAEC,MAAM,EAAE,mBAAmB;cAAEC,aAAa,EAAE,gBAAgB;cAAEC,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM;YAAE,CAAC;UACjH,KAAK,OAAO;YACV,OAAO;cAAE,GAAGH,OAAO;cAAEC,MAAM,EAAE,UAAU;cAAEC,aAAa,EAAE,aAAa;cAAEC,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM;YAAE,CAAC;UACrG,KAAK,MAAM;YACT,OAAO;cAAE,GAAGH,OAAO;cAAEC,MAAM,EAAE,UAAU;cAAEC,aAAa,EAAE,SAAS;cAAEC,OAAO,EAAE,CAAC,OAAO;YAAE,CAAC;UACzF;YACE,OAAOH,OAAO;QAClB;MACF;MACA,OAAOA,OAAO;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,mBAAmB,GAAGA,CAACR,SAAS,EAAES,UAAU,KAAK;IACrD7C,mBAAmB,CAACsB,IAAI,IAAI;MAC1B,IAAIuB,UAAU,EAAE;QACd,OAAO,CAAC,GAAGvB,IAAI,EAAEc,SAAS,CAAC;MAC7B,CAAC,MAAM;QACL,OAAOd,IAAI,CAACwB,MAAM,CAAC9B,EAAE,IAAIA,EAAE,KAAKoB,SAAS,CAAC;MAC5C;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMW,eAAe,GAAIF,UAAU,IAAK;IACtC,IAAIA,UAAU,EAAE;MACd7C,mBAAmB,CAACL,QAAQ,CAAC4C,GAAG,CAACS,CAAC,IAAIA,CAAC,CAAChC,EAAE,CAAC,CAAC;IAC9C,CAAC,MAAM;MACLhB,mBAAmB,CAAC,EAAE,CAAC;IACzB;EACF,CAAC;EAED,MAAMiD,gBAAgB,GAAIZ,MAAM,IAAK;IACnChB,OAAO,CAACiB,GAAG,CAAC,eAAeD,MAAM,gBAAgB,EAAEtC,gBAAgB,CAAC;IACpEA,gBAAgB,CAACmD,OAAO,CAACd,SAAS,IAAI;MACpCD,mBAAmB,CAACC,SAAS,EAAEC,MAAM,CAAC;IACxC,CAAC,CAAC;EACJ,CAAC;EAED,MAAMc,mBAAmB,GAAIC,WAAW,IAAK;IAC3C/B,OAAO,CAACiB,GAAG,CAAC,mBAAmB,EAAEc,WAAW,CAAC;;IAE7C;IACA,MAAMC,UAAU,GAAG;MACjBrC,EAAE,EAAErB,QAAQ,CAACoC,MAAM,GAAG,CAAC;MACvBuB,GAAG,EAAE3D,QAAQ,CAACoC,MAAM,GAAG,CAAC;MACxBC,QAAQ,EAAEoB,WAAW,CAACG,WAAW;MACjCC,KAAK,EAAEJ,WAAW,CAACK,SAAS,KAAK,UAAU,GAAG,eAAe,GAAG,GAAGL,WAAW,CAACM,IAAI,IAAIN,WAAW,CAACO,IAAI,EAAE;MACzGlB,MAAM,EAAE,gBAAgB;MACxBmB,iBAAiB,EAAE,CAAC;MACpBC,cAAc,EAAE,CAAC;MACjBnB,aAAa,EAAE,eAAe;MAC9BC,OAAO,EAAE,CAAC,OAAO;IACnB,CAAC;IAED/C,WAAW,CAAC0B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE+B,UAAU,CAAC,CAAC;IAC1CvD,kBAAkB,CAAC,KAAK,CAAC;;IAEzB;IACA,MAAMgE,MAAM,GAAG;MACb9C,EAAE,EAAER,IAAI,CAACuB,MAAM,GAAG,CAAC;MACnBd,IAAI,EAAE,IAAIM,IAAI,CAAC,CAAC,CAACE,kBAAkB,CAAC,CAAC;MACrCP,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,YAAYiC,WAAW,CAACG,WAAW;IAC9C,CAAC;IACD9C,OAAO,CAACa,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEwC,MAAM,CAAC,CAAC;EACpC,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5BtD,OAAO,CAAC,EAAE,CAAC;EACb,CAAC;EAED,MAAMuD,gBAAgB,GAAGA,CAAA,KAAM;IAC7B3C,OAAO,CAACiB,GAAG,CAAC,mBAAmB,CAAC;EAClC,CAAC;EAED,IAAI5B,OAAO,EAAE;IACX,oBACElB,OAAA;MAAKyE,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpD1E,OAAA;QAAKyE,SAAS,EAAC;MAAgE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC;EAEV;EAEA,oBACE9E,OAAA;IAAKyE,SAAS,EAAC,6EAA6E;IAAAC,QAAA,gBAE1F1E,OAAA;MAAKyE,SAAS,EAAC,uDAAuD;MAAAC,QAAA,eACpE1E,OAAA;QAAKyE,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB1E,OAAA;UAAKyE,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD1E,OAAA;YAAIyE,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAAC;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9E9E,OAAA;YAAKyE,SAAS,EAAC,6BAA6B;YAAAC,QAAA,eAC1C1E,OAAA;cAAKyE,SAAS,EAAC,iEAAiE;cAAAC,QAAA,gBAC9E1E,OAAA;gBAAKyE,SAAS,EAAC;cAA+B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrD9E,OAAA;gBAAMyE,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN9E,OAAA;MAAKyE,SAAS,EAAC,KAAK;MAAAC,QAAA,eAClB1E,OAAA;QAAKyE,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpD1E,OAAA;UAAKyE,SAAS,EAAC,yBAAyB;UAAAC,QAAA,eAEtC1E,OAAA;YAAKyE,SAAS,EAAC,+BAA+B;YAAAC,QAAA,gBAC5C1E,OAAA;cAAKyE,SAAS,EAAC,8BAA8B;cAAAC,QAAA,eAC3C1E,OAAA;gBAAKyE,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChD1E,OAAA;kBAAIyE,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9E9E,OAAA;kBAAKyE,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C1E,OAAA;oBACE+E,OAAO,EAAEA,CAAA,KAAMzE,kBAAkB,CAAC,IAAI,CAAE;oBACxCmE,SAAS,EAAC,6GAA6G;oBAAAC,QAAA,gBAEvH1E,OAAA,CAACT,MAAM;sBAACkF,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC9B9E,OAAA;sBAAA0E,QAAA,EAAM;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC,eACT9E,OAAA;oBACE+E,OAAO,EAAEA,CAAA,KAAMtB,gBAAgB,CAAC,OAAO,CAAE;oBACzCgB,SAAS,EAAC,+GAA+G;oBAAAC,QAAA,gBAEzH1E,OAAA,CAACR,MAAM;sBAACiF,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC9B9E,OAAA;sBAAA0E,QAAA,EAAM;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACT9E,OAAA;oBACE+E,OAAO,EAAEA,CAAA,KAAMtB,gBAAgB,CAAC,MAAM,CAAE;oBACxCgB,SAAS,EAAC,2GAA2G;oBAAAC,QAAA,gBAErH1E,OAAA,CAACN,YAAY;sBAAC+E,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACpC9E,OAAA;sBAAA0E,QAAA,EAAM;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN9E,OAAA,CAACL,YAAY;cACXQ,QAAQ,EAAEA,QAAS;cACnBI,gBAAgB,EAAEA,gBAAiB;cACnCyE,eAAe,EAAE5B,mBAAoB;cACrC6B,eAAe,EAAEtC,mBAAoB;cACrCuC,WAAW,EAAE3B;YAAgB;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN9E,OAAA;UAAKyE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExB1E,OAAA;YAAKyE,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD1E,OAAA;cAAIyE,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAElF9E,OAAA;cAAKyE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB1E,OAAA;gBAAA0E,QAAA,gBACE1E,OAAA;kBAAOyE,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrF9E,OAAA;kBAAKyE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7B1E,OAAA;oBAAQyE,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACrF9E,OAAA;oBAAQyE,SAAS,EAAC,qDAAqD;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxF9E,OAAA;oBAAQyE,SAAS,EAAC,qDAAqD;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN9E,OAAA;gBAAA0E,QAAA,gBACE1E,OAAA;kBAAOyE,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,GAAC,6BAClD,EAACnE,gBAAgB,CAACgC,MAAM,EAAC,yCACvC;gBAAA;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR9E,OAAA;kBAAKyE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7B1E,OAAA;oBACE+E,OAAO,EAAEA,CAAA,KAAMtB,gBAAgB,CAAC,OAAO,CAAE;oBACzC0B,QAAQ,EAAE5E,gBAAgB,CAACgC,MAAM,KAAK,CAAE;oBACxCkC,SAAS,EAAC,+HAA+H;oBAAAC,QAAA,gBAEzI1E,OAAA,CAACR,MAAM;sBAACiF,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC9B9E,OAAA;sBAAA0E,QAAA,EAAM;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC,eACT9E,OAAA;oBACE+E,OAAO,EAAEA,CAAA,KAAMtB,gBAAgB,CAAC,OAAO,CAAE;oBACzC0B,QAAQ,EAAE5E,gBAAgB,CAACgC,MAAM,KAAK,CAAE;oBACxCkC,SAAS,EAAC,gIAAgI;oBAAAC,QAAA,gBAE1I1E,OAAA,CAACP,OAAO;sBAACgF,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC/B9E,OAAA;sBAAA0E,QAAA,EAAM;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,eACT9E,OAAA;oBACE+E,OAAO,EAAEA,CAAA,KAAMtB,gBAAgB,CAAC,MAAM,CAAE;oBACxC0B,QAAQ,EAAE5E,gBAAgB,CAACgC,MAAM,KAAK,CAAE;oBACxCkC,SAAS,EAAC,6HAA6H;oBAAAC,QAAA,gBAEvI1E,OAAA,CAACN,YAAY;sBAAC+E,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACpC9E,OAAA;sBAAA0E,QAAA,EAAM;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN9E,OAAA;gBAAA0E,QAAA,gBACE1E,OAAA;kBAAOyE,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxF9E,OAAA;kBAAKyE,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxB1E,OAAA;oBAAA0E,QAAA,gBACE1E,OAAA;sBAAOyE,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAChF9E,OAAA;sBACEoF,IAAI,EAAC,MAAM;sBACXC,WAAW,EAAC,kCAAkC;sBAC9CZ,SAAS,EAAC;oBAAyD;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eAEN9E,OAAA;oBAAKyE,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrC1E,OAAA;sBAAA0E,QAAA,gBACE1E,OAAA;wBAAOyE,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAC;sBAAY;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACxE9E,OAAA;wBAAOoF,IAAI,EAAC,QAAQ;wBAACE,YAAY,EAAC,GAAG;wBAACb,SAAS,EAAC;sBAAyD;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzG,CAAC,eACN9E,OAAA;sBAAA0E,QAAA,gBACE1E,OAAA;wBAAOyE,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAC;sBAAoB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAChF9E,OAAA;wBAAOoF,IAAI,EAAC,MAAM;wBAACE,YAAY,EAAC,IAAI;wBAACb,SAAS,EAAC;sBAAyD;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN9E,OAAA;oBAAKyE,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrC1E,OAAA;sBAAA0E,QAAA,gBACE1E,OAAA;wBAAOyE,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAC;sBAAkB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC9E9E,OAAA;wBAAOoF,IAAI,EAAC,QAAQ;wBAACE,YAAY,EAAC,IAAI;wBAACb,SAAS,EAAC;sBAAyD;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1G,CAAC,eACN9E,OAAA;sBAAA0E,QAAA,gBACE1E,OAAA;wBAAOyE,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAC;sBAA6B;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACzF9E,OAAA;wBAAOoF,IAAI,EAAC,MAAM;wBAACE,YAAY,EAAC,MAAM;wBAACb,SAAS,EAAC;sBAAyD;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1G,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN9E,OAAA;oBAAQyE,SAAS,EAAC,gFAAgF;oBAAAC,QAAA,EAAC;kBAEnG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN9E,OAAA,CAACH,UAAU;YACTmB,IAAI,EAAEA,IAAK;YACXuE,WAAW,EAAEhB,eAAgB;YAC7BiB,YAAY,EAAEhB;UAAiB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9E,OAAA,CAACJ,WAAW;MACV6F,MAAM,EAAEpF,eAAgB;MACxBqF,OAAO,EAAEA,CAAA,KAAMpF,kBAAkB,CAAC,KAAK,CAAE;MACzCqF,QAAQ,EAAEhC;IAAoB;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC5E,EAAA,CAzXID,SAAS;AAAA2F,EAAA,GAAT3F,SAAS;AA2Xf,eAAeA,SAAS;AAAC,IAAA2F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}