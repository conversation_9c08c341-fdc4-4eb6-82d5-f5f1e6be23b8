{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Dashboard/TasksOverview.jsx\",\n  _s = $RefreshSig$();\n/**\n * Tasks Overview Component for Dashboard\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { FiPlay, FiPause, FiClock, FiCheckCircle, FiAlertCircle, FiMoreHorizontal } from 'react-icons/fi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TasksOverview = () => {\n  _s();\n  const [tasks, setTasks] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Fetch tasks data\n  useEffect(() => {\n    const fetchTasks = async () => {\n      try {\n        let baseUrl = 'http://localhost:8000';\n        if (window.electronAPI) {\n          baseUrl = await window.electronAPI.getBackendUrl();\n        }\n        const response = await fetch(`${baseUrl}/api/v1/tasks/?active_only=true&limit=5`);\n        const data = await response.json();\n        setTasks(Array.isArray(data) ? data : []);\n      } catch (error) {\n        console.error('Failed to fetch tasks:', error);\n        // Mock data for development\n        setTasks([{\n          id: 1,\n          name: 'Follow @competitor1 followers',\n          status: 'running',\n          progress_percentage: 65,\n          total_processed: 130,\n          target_count: 200,\n          task_type: 'follow_followers'\n        }, {\n          id: 2,\n          name: 'Unfollow inactive accounts',\n          status: 'paused',\n          progress_percentage: 30,\n          total_processed: 45,\n          target_count: 150,\n          task_type: 'unfollow_users'\n        }, {\n          id: 3,\n          name: 'Follow @competitor2 followers',\n          status: 'pending',\n          progress_percentage: 0,\n          total_processed: 0,\n          target_count: 100,\n          task_type: 'follow_followers'\n        }]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchTasks();\n    const interval = setInterval(fetchTasks, 10000); // Refresh every 10 seconds\n    return () => clearInterval(interval);\n  }, []);\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'running':\n        return /*#__PURE__*/_jsxDEV(FiPlay, {\n          className: \"w-4 h-4 text-green-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 16\n        }, this);\n      case 'paused':\n        return /*#__PURE__*/_jsxDEV(FiPause, {\n          className: \"w-4 h-4 text-yellow-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 16\n        }, this);\n      case 'completed':\n        return /*#__PURE__*/_jsxDEV(FiCheckCircle, {\n          className: \"w-4 h-4 text-blue-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 16\n        }, this);\n      case 'failed':\n        return /*#__PURE__*/_jsxDEV(FiAlertCircle, {\n          className: \"w-4 h-4 text-red-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FiClock, {\n          className: \"w-4 h-4 text-gray-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'running':\n        return 'bg-green-100 text-green-800';\n      case 'paused':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'completed':\n        return 'bg-blue-100 text-blue-800';\n      case 'failed':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  const getTaskTypeLabel = taskType => {\n    switch (taskType) {\n      case 'follow_followers':\n        return 'Follow Followers';\n      case 'follow_following':\n        return 'Follow Following';\n      case 'unfollow_users':\n        return 'Unfollow Users';\n      default:\n        return 'Unknown';\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-lg font-semibold text-gray-900 mb-4\",\n        children: \"Active Tasks\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [1, 2, 3].map(i => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-pulse\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-2 bg-gray-200 rounded w-full mb-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-3 bg-gray-200 rounded w-1/2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 15\n          }, this)]\n        }, i, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-lg font-semibold text-gray-900\",\n        children: \"Active Tasks\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => window.location.href = '/tasks',\n        className: \"text-sm text-blue-600 hover:text-blue-800 font-medium\",\n        children: \"View All\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this), tasks.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-8\",\n      children: [/*#__PURE__*/_jsxDEV(FiPlay, {\n        className: \"w-12 h-12 text-gray-300 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-2\",\n        children: \"No Active Tasks\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 mb-4\",\n        children: \"Create your first automation task to get started.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => window.location.href = '/tasks/new',\n        className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n        children: \"Create Task\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: tasks.map((task, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: index * 0.1\n        },\n        className: \"border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [getStatusIcon(task.status), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-medium text-gray-900\",\n                children: task.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: getTaskTypeLabel(task.task_type)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: `px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(task.status)}`,\n              children: task.status.charAt(0).toUpperCase() + task.status.slice(1)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"p-1 text-gray-400 hover:text-gray-600\",\n              children: /*#__PURE__*/_jsxDEV(FiMoreHorizontal, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between text-sm text-gray-600 mb-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [task.progress_percentage, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-200 rounded-full h-2\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"bg-blue-600 h-2 rounded-full\",\n              initial: {\n                width: 0\n              },\n              animate: {\n                width: `${task.progress_percentage}%`\n              },\n              transition: {\n                duration: 0.5\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between text-sm text-gray-600\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [task.total_processed, \" / \", task.target_count, \" completed\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 17\n          }, this), task.status === 'running' && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 21\n            }, this), \"Running\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 15\n        }, this)]\n      }, task.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 135,\n    columnNumber: 5\n  }, this);\n};\n_s(TasksOverview, \"suOgpW3t2nMZzUgwyaGqSRlDRtE=\");\n_c = TasksOverview;\nexport default TasksOverview;\nvar _c;\n$RefreshReg$(_c, \"TasksOverview\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "FiPlay", "FiPause", "<PERSON><PERSON><PERSON>", "FiCheckCircle", "FiAlertCircle", "FiMoreHorizontal", "jsxDEV", "_jsxDEV", "TasksOverview", "_s", "tasks", "setTasks", "loading", "setLoading", "fetchTasks", "baseUrl", "window", "electronAPI", "getBackendUrl", "response", "fetch", "data", "json", "Array", "isArray", "error", "console", "id", "name", "status", "progress_percentage", "total_processed", "target_count", "task_type", "interval", "setInterval", "clearInterval", "getStatusIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getStatusColor", "getTaskTypeLabel", "taskType", "children", "map", "i", "onClick", "location", "href", "length", "task", "index", "div", "initial", "opacity", "y", "animate", "transition", "delay", "char<PERSON>t", "toUpperCase", "slice", "width", "duration", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Dashboard/TasksOverview.jsx"], "sourcesContent": ["/**\n * Tasks Overview Component for Dashboard\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  FiPlay,\n  FiPause,\n  FiClock,\n  FiCheckCircle,\n  FiAlertCircle,\n  FiMoreHorizontal\n} from 'react-icons/fi';\n\nconst TasksOverview = () => {\n  const [tasks, setTasks] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Fetch tasks data\n  useEffect(() => {\n    const fetchTasks = async () => {\n      try {\n        let baseUrl = 'http://localhost:8000';\n        if (window.electronAPI) {\n          baseUrl = await window.electronAPI.getBackendUrl();\n        }\n\n        const response = await fetch(`${baseUrl}/api/v1/tasks/?active_only=true&limit=5`);\n        const data = await response.json();\n        setTasks(Array.isArray(data) ? data : []);\n      } catch (error) {\n        console.error('Failed to fetch tasks:', error);\n        // Mock data for development\n        setTasks([\n          {\n            id: 1,\n            name: 'Follow @competitor1 followers',\n            status: 'running',\n            progress_percentage: 65,\n            total_processed: 130,\n            target_count: 200,\n            task_type: 'follow_followers'\n          },\n          {\n            id: 2,\n            name: 'Unfollow inactive accounts',\n            status: 'paused',\n            progress_percentage: 30,\n            total_processed: 45,\n            target_count: 150,\n            task_type: 'unfollow_users'\n          },\n          {\n            id: 3,\n            name: 'Follow @competitor2 followers',\n            status: 'pending',\n            progress_percentage: 0,\n            total_processed: 0,\n            target_count: 100,\n            task_type: 'follow_followers'\n          }\n        ]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchTasks();\n    const interval = setInterval(fetchTasks, 10000); // Refresh every 10 seconds\n    return () => clearInterval(interval);\n  }, []);\n\n  const getStatusIcon = (status) => {\n    switch (status) {\n      case 'running':\n        return <FiPlay className=\"w-4 h-4 text-green-600\" />;\n      case 'paused':\n        return <FiPause className=\"w-4 h-4 text-yellow-600\" />;\n      case 'completed':\n        return <FiCheckCircle className=\"w-4 h-4 text-blue-600\" />;\n      case 'failed':\n        return <FiAlertCircle className=\"w-4 h-4 text-red-600\" />;\n      default:\n        return <FiClock className=\"w-4 h-4 text-gray-600\" />;\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'running':\n        return 'bg-green-100 text-green-800';\n      case 'paused':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'completed':\n        return 'bg-blue-100 text-blue-800';\n      case 'failed':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getTaskTypeLabel = (taskType) => {\n    switch (taskType) {\n      case 'follow_followers':\n        return 'Follow Followers';\n      case 'follow_following':\n        return 'Follow Following';\n      case 'unfollow_users':\n        return 'Unfollow Users';\n      default:\n        return 'Unknown';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Active Tasks</h2>\n        <div className=\"space-y-4\">\n          {[1, 2, 3].map((i) => (\n            <div key={i} className=\"animate-pulse\">\n              <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n              <div className=\"h-2 bg-gray-200 rounded w-full mb-2\"></div>\n              <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n            </div>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900\">Active Tasks</h2>\n        <button\n          onClick={() => window.location.href = '/tasks'}\n          className=\"text-sm text-blue-600 hover:text-blue-800 font-medium\"\n        >\n          View All\n        </button>\n      </div>\n\n      {tasks.length === 0 ? (\n        <div className=\"text-center py-8\">\n          <FiPlay className=\"w-12 h-12 text-gray-300 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No Active Tasks</h3>\n          <p className=\"text-gray-600 mb-4\">Create your first automation task to get started.</p>\n          <button\n            onClick={() => window.location.href = '/tasks/new'}\n            className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            Create Task\n          </button>\n        </div>\n      ) : (\n        <div className=\"space-y-4\">\n          {tasks.map((task, index) => (\n            <motion.div\n              key={task.id}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.1 }}\n              className=\"border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors\"\n            >\n              <div className=\"flex items-center justify-between mb-3\">\n                <div className=\"flex items-center space-x-3\">\n                  {getStatusIcon(task.status)}\n                  <div>\n                    <h3 className=\"font-medium text-gray-900\">{task.name}</h3>\n                    <p className=\"text-sm text-gray-600\">\n                      {getTaskTypeLabel(task.task_type)}\n                    </p>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-center space-x-2\">\n                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(task.status)}`}>\n                    {task.status.charAt(0).toUpperCase() + task.status.slice(1)}\n                  </span>\n                  <button className=\"p-1 text-gray-400 hover:text-gray-600\">\n                    <FiMoreHorizontal className=\"w-4 h-4\" />\n                  </button>\n                </div>\n              </div>\n\n              {/* Progress Bar */}\n              <div className=\"mb-3\">\n                <div className=\"flex items-center justify-between text-sm text-gray-600 mb-1\">\n                  <span>Progress</span>\n                  <span>{task.progress_percentage}%</span>\n                </div>\n                <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                  <motion.div\n                    className=\"bg-blue-600 h-2 rounded-full\"\n                    initial={{ width: 0 }}\n                    animate={{ width: `${task.progress_percentage}%` }}\n                    transition={{ duration: 0.5 }}\n                  />\n                </div>\n              </div>\n\n              {/* Stats */}\n              <div className=\"flex items-center justify-between text-sm text-gray-600\">\n                <span>\n                  {task.total_processed} / {task.target_count} completed\n                </span>\n                {task.status === 'running' && (\n                  <span className=\"flex items-center\">\n                    <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2\"></div>\n                    Running\n                  </span>\n                )}\n              </div>\n            </motion.div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TasksOverview;\n"], "mappings": ";;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,MAAM,EACNC,OAAO,EACPC,OAAO,EACPC,aAAa,EACbC,aAAa,EACbC,gBAAgB,QACX,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,MAAMgB,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI;QACF,IAAIC,OAAO,GAAG,uBAAuB;QACrC,IAAIC,MAAM,CAACC,WAAW,EAAE;UACtBF,OAAO,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,aAAa,CAAC,CAAC;QACpD;QAEA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,OAAO,yCAAyC,CAAC;QACjF,MAAMM,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAClCX,QAAQ,CAACY,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,GAAGA,IAAI,GAAG,EAAE,CAAC;MAC3C,CAAC,CAAC,OAAOI,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C;QACAd,QAAQ,CAAC,CACP;UACEgB,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,+BAA+B;UACrCC,MAAM,EAAE,SAAS;UACjBC,mBAAmB,EAAE,EAAE;UACvBC,eAAe,EAAE,GAAG;UACpBC,YAAY,EAAE,GAAG;UACjBC,SAAS,EAAE;QACb,CAAC,EACD;UACEN,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,4BAA4B;UAClCC,MAAM,EAAE,QAAQ;UAChBC,mBAAmB,EAAE,EAAE;UACvBC,eAAe,EAAE,EAAE;UACnBC,YAAY,EAAE,GAAG;UACjBC,SAAS,EAAE;QACb,CAAC,EACD;UACEN,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,+BAA+B;UACrCC,MAAM,EAAE,SAAS;UACjBC,mBAAmB,EAAE,CAAC;UACtBC,eAAe,EAAE,CAAC;UAClBC,YAAY,EAAE,GAAG;UACjBC,SAAS,EAAE;QACb,CAAC,CACF,CAAC;MACJ,CAAC,SAAS;QACRpB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDC,UAAU,CAAC,CAAC;IACZ,MAAMoB,QAAQ,GAAGC,WAAW,CAACrB,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC;IACjD,OAAO,MAAMsB,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,aAAa,GAAIR,MAAM,IAAK;IAChC,QAAQA,MAAM;MACZ,KAAK,SAAS;QACZ,oBAAOtB,OAAA,CAACP,MAAM;UAACsC,SAAS,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD,KAAK,QAAQ;QACX,oBAAOnC,OAAA,CAACN,OAAO;UAACqC,SAAS,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxD,KAAK,WAAW;QACd,oBAAOnC,OAAA,CAACJ,aAAa;UAACmC,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5D,KAAK,QAAQ;QACX,oBAAOnC,OAAA,CAACH,aAAa;UAACkC,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3D;QACE,oBAAOnC,OAAA,CAACL,OAAO;UAACoC,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACxD;EACF,CAAC;EAED,MAAMC,cAAc,GAAId,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,SAAS;QACZ,OAAO,6BAA6B;MACtC,KAAK,QAAQ;QACX,OAAO,+BAA+B;MACxC,KAAK,WAAW;QACd,OAAO,2BAA2B;MACpC,KAAK,QAAQ;QACX,OAAO,yBAAyB;MAClC;QACE,OAAO,2BAA2B;IACtC;EACF,CAAC;EAED,MAAMe,gBAAgB,GAAIC,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,kBAAkB;QACrB,OAAO,kBAAkB;MAC3B,KAAK,kBAAkB;QACrB,OAAO,kBAAkB;MAC3B,KAAK,gBAAgB;QACnB,OAAO,gBAAgB;MACzB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,IAAIjC,OAAO,EAAE;IACX,oBACEL,OAAA;MAAK+B,SAAS,EAAC,0DAA0D;MAAAQ,QAAA,gBACvEvC,OAAA;QAAI+B,SAAS,EAAC,0CAA0C;QAAAQ,QAAA,EAAC;MAAY;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1EnC,OAAA;QAAK+B,SAAS,EAAC,WAAW;QAAAQ,QAAA,EACvB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEC,CAAC,iBACfzC,OAAA;UAAa+B,SAAS,EAAC,eAAe;UAAAQ,QAAA,gBACpCvC,OAAA;YAAK+B,SAAS,EAAC;UAAoC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1DnC,OAAA;YAAK+B,SAAS,EAAC;UAAqC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3DnC,OAAA;YAAK+B,SAAS,EAAC;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GAH7CM,CAAC;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIN,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEnC,OAAA;IAAK+B,SAAS,EAAC,0DAA0D;IAAAQ,QAAA,gBACvEvC,OAAA;MAAK+B,SAAS,EAAC,wCAAwC;MAAAQ,QAAA,gBACrDvC,OAAA;QAAI+B,SAAS,EAAC,qCAAqC;QAAAQ,QAAA,EAAC;MAAY;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrEnC,OAAA;QACE0C,OAAO,EAAEA,CAAA,KAAMjC,MAAM,CAACkC,QAAQ,CAACC,IAAI,GAAG,QAAS;QAC/Cb,SAAS,EAAC,uDAAuD;QAAAQ,QAAA,EAClE;MAED;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELhC,KAAK,CAAC0C,MAAM,KAAK,CAAC,gBACjB7C,OAAA;MAAK+B,SAAS,EAAC,kBAAkB;MAAAQ,QAAA,gBAC/BvC,OAAA,CAACP,MAAM;QAACsC,SAAS,EAAC;MAAsC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3DnC,OAAA;QAAI+B,SAAS,EAAC,wCAAwC;QAAAQ,QAAA,EAAC;MAAe;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3EnC,OAAA;QAAG+B,SAAS,EAAC,oBAAoB;QAAAQ,QAAA,EAAC;MAAiD;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACvFnC,OAAA;QACE0C,OAAO,EAAEA,CAAA,KAAMjC,MAAM,CAACkC,QAAQ,CAACC,IAAI,GAAG,YAAa;QACnDb,SAAS,EAAC,iFAAiF;QAAAQ,QAAA,EAC5F;MAED;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,gBAENnC,OAAA;MAAK+B,SAAS,EAAC,WAAW;MAAAQ,QAAA,EACvBpC,KAAK,CAACqC,GAAG,CAAC,CAACM,IAAI,EAAEC,KAAK,kBACrB/C,OAAA,CAACR,MAAM,CAACwD,GAAG;QAETC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAEP,KAAK,GAAG;QAAI,CAAE;QACnChB,SAAS,EAAC,+EAA+E;QAAAQ,QAAA,gBAEzFvC,OAAA;UAAK+B,SAAS,EAAC,wCAAwC;UAAAQ,QAAA,gBACrDvC,OAAA;YAAK+B,SAAS,EAAC,6BAA6B;YAAAQ,QAAA,GACzCT,aAAa,CAACgB,IAAI,CAACxB,MAAM,CAAC,eAC3BtB,OAAA;cAAAuC,QAAA,gBACEvC,OAAA;gBAAI+B,SAAS,EAAC,2BAA2B;gBAAAQ,QAAA,EAAEO,IAAI,CAACzB;cAAI;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1DnC,OAAA;gBAAG+B,SAAS,EAAC,uBAAuB;gBAAAQ,QAAA,EACjCF,gBAAgB,CAACS,IAAI,CAACpB,SAAS;cAAC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnC,OAAA;YAAK+B,SAAS,EAAC,6BAA6B;YAAAQ,QAAA,gBAC1CvC,OAAA;cAAM+B,SAAS,EAAE,8CAA8CK,cAAc,CAACU,IAAI,CAACxB,MAAM,CAAC,EAAG;cAAAiB,QAAA,EAC1FO,IAAI,CAACxB,MAAM,CAACiC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGV,IAAI,CAACxB,MAAM,CAACmC,KAAK,CAAC,CAAC;YAAC;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eACPnC,OAAA;cAAQ+B,SAAS,EAAC,uCAAuC;cAAAQ,QAAA,eACvDvC,OAAA,CAACF,gBAAgB;gBAACiC,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnC,OAAA;UAAK+B,SAAS,EAAC,MAAM;UAAAQ,QAAA,gBACnBvC,OAAA;YAAK+B,SAAS,EAAC,8DAA8D;YAAAQ,QAAA,gBAC3EvC,OAAA;cAAAuC,QAAA,EAAM;YAAQ;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrBnC,OAAA;cAAAuC,QAAA,GAAOO,IAAI,CAACvB,mBAAmB,EAAC,GAAC;YAAA;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACNnC,OAAA;YAAK+B,SAAS,EAAC,qCAAqC;YAAAQ,QAAA,eAClDvC,OAAA,CAACR,MAAM,CAACwD,GAAG;cACTjB,SAAS,EAAC,8BAA8B;cACxCkB,OAAO,EAAE;gBAAES,KAAK,EAAE;cAAE,CAAE;cACtBN,OAAO,EAAE;gBAAEM,KAAK,EAAE,GAAGZ,IAAI,CAACvB,mBAAmB;cAAI,CAAE;cACnD8B,UAAU,EAAE;gBAAEM,QAAQ,EAAE;cAAI;YAAE;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnC,OAAA;UAAK+B,SAAS,EAAC,yDAAyD;UAAAQ,QAAA,gBACtEvC,OAAA;YAAAuC,QAAA,GACGO,IAAI,CAACtB,eAAe,EAAC,KAAG,EAACsB,IAAI,CAACrB,YAAY,EAAC,YAC9C;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACNW,IAAI,CAACxB,MAAM,KAAK,SAAS,iBACxBtB,OAAA;YAAM+B,SAAS,EAAC,mBAAmB;YAAAQ,QAAA,gBACjCvC,OAAA;cAAK+B,SAAS,EAAC;YAAsD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,WAE9E;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA,GAtDDW,IAAI,CAAC1B,EAAE;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAuDF,CACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACjC,EAAA,CA/MID,aAAa;AAAA2D,EAAA,GAAb3D,aAAa;AAiNnB,eAAeA,aAAa;AAAC,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}