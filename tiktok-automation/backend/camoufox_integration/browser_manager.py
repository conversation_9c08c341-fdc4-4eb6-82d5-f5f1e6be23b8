"""
Browser Manager for Camoufox integration with antidetect features
"""

import asyncio
import json
import os
import tempfile
import time
from typing import Dict, Any, Optional, List
from pathlib import Path
import psutil
from loguru import logger

# Browser types from Camoufox (compatible with Playwright API)
from typing import Any as Browser, Any as BrowserContext, Any as Page

from core.config import settings
from models.browser_profile import BrowserProfile
from models.proxy import Proxy
from .fingerprint_generator import FingerprintGenerator
from .proxy_manager import ProxyManager
from .antidetect_config import AntidetectConfig
from .camoufox_wrapper import local_camoufox


class BrowserManager:
    """Manages Camoufox browser instances with antidetect capabilities"""

    def __init__(self):
        self.active_browsers: Dict[str, Browser] = {}
        self.active_contexts: Dict[str, BrowserContext] = {}
        self.fingerprint_generator = FingerprintGenerator()
        self.proxy_manager = ProxyManager()
        self.antidetect_config = AntidetectConfig()

        # Performance tracking
        self.browser_count = 0
        self.max_browsers = settings.MAX_CONCURRENT_BROWSERS

        # Initialization flag
        self._initialized = False

    async def initialize(self) -> bool:
        """Initialize browser manager and ensure Camoufox is ready"""

        if self._initialized:
            return True

        try:
            # Initialize local Camoufox
            success = await local_camoufox.initialize()
            if success:
                self._initialized = True
                logger.info("BrowserManager initialized successfully")
            else:
                logger.error("Failed to initialize Camoufox")

            return success

        except Exception as e:
            logger.error(f"BrowserManager initialization failed: {e}")
            return False
        
    async def create_browser_instance(
        self,
        profile: BrowserProfile,
        proxy: Optional[Proxy] = None,
        headless: bool = None,
        custom_config: Optional[Dict[str, Any]] = None
    ) -> Browser:
        """Create a new Camoufox browser instance with antidetect configuration"""

        # Ensure initialized
        if not self._initialized:
            if not await self.initialize():
                raise Exception("Failed to initialize BrowserManager")

        # Check browser limits
        if self.browser_count >= self.max_browsers:
            raise Exception(f"Maximum browser limit reached ({self.max_browsers})")

        try:
            # Generate fingerprint configuration
            fingerprint_config = await self._generate_fingerprint_config(profile, custom_config)

            # Setup proxy configuration
            proxy_config = None
            if proxy:
                proxy_config = await self.proxy_manager.get_proxy_config(proxy)

            # Create user data directory
            user_data_dir = await self._create_user_data_dir(profile.id)

            logger.info(f"Creating browser instance for profile {profile.name}")

            # Create browser using local Camoufox
            browser = await local_camoufox.create_browser(
                config=fingerprint_config,
                proxy=proxy_config,
                headless=headless if headless is not None else settings.CAMOUFOX_HEADLESS,
                user_data_dir=user_data_dir,
                timeout=settings.CAMOUFOX_TIMEOUT
            )
            
            # Store browser reference
            browser_id = f"profile_{profile.id}_{id(browser)}"
            self.active_browsers[browser_id] = browser
            self.browser_count += 1
            
            # Update profile usage
            profile.update_usage()
            
            logger.info(f"Browser instance created successfully: {browser_id}")
            return browser
            
        except Exception as e:
            logger.error(f"Failed to create browser instance: {e}")
            raise



    async def create_browser_context(
        self,
        browser: Browser,
        profile: BrowserProfile,
        proxy: Optional[Proxy] = None
    ) -> BrowserContext:
        """Create a new browser context with antidetect settings"""
        
        try:
            # Generate context configuration
            context_config = await self._get_context_config(profile, proxy)
            
            # Create context
            context = await browser.new_context(**context_config)
            
            # Apply additional antidetect measures
            await self._apply_antidetect_measures(context, profile)
            
            # Store context reference
            context_id = f"profile_{profile.id}_{id(context)}"
            self.active_contexts[context_id] = context
            
            logger.info(f"Browser context created: {context_id}")
            return context
            
        except Exception as e:
            logger.error(f"Failed to create browser context: {e}")
            raise
    
    async def close_browser(self, browser: Browser):
        """Close browser instance and cleanup"""
        try:
            # Find and remove browser from active list
            browser_id = None
            for bid, b in self.active_browsers.items():
                if b == browser:
                    browser_id = bid
                    break
            
            if browser_id:
                del self.active_browsers[browser_id]
                self.browser_count -= 1
            
            # Close browser
            await browser.close()
            
            logger.info(f"Browser closed: {browser_id}")
            
        except Exception as e:
            logger.error(f"Error closing browser: {e}")
    
    async def close_context(self, context: BrowserContext):
        """Close browser context"""
        try:
            # Find and remove context from active list
            context_id = None
            for cid, c in self.active_contexts.items():
                if c == context:
                    context_id = cid
                    break
            
            if context_id:
                del self.active_contexts[context_id]
            
            # Close context
            await context.close()
            
            logger.info(f"Browser context closed: {context_id}")
            
        except Exception as e:
            logger.error(f"Error closing context: {e}")
    
    async def get_memory_usage(self) -> Dict[str, Any]:
        """Get memory usage statistics"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            
            return {
                "total_browsers": self.browser_count,
                "max_browsers": self.max_browsers,
                "memory_rss_mb": memory_info.rss / 1024 / 1024,
                "memory_vms_mb": memory_info.vms / 1024 / 1024,
                "memory_percent": process.memory_percent(),
                "cpu_percent": process.cpu_percent()
            }
        except Exception as e:
            logger.error(f"Error getting memory usage: {e}")
            return {}
    
    async def cleanup_all(self):
        """Cleanup all active browsers and contexts"""
        logger.info("Cleaning up all browser instances...")
        
        # Close all contexts
        for context in list(self.active_contexts.values()):
            try:
                await context.close()
            except Exception as e:
                logger.error(f"Error closing context: {e}")
        
        # Close all browsers
        for browser in list(self.active_browsers.values()):
            try:
                await browser.close()
            except Exception as e:
                logger.error(f"Error closing browser: {e}")
        
        self.active_browsers.clear()
        self.active_contexts.clear()
        self.browser_count = 0
        
        logger.info("Browser cleanup completed")
    
    async def _generate_fingerprint_config(
        self,
        profile: BrowserProfile,
        custom_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Generate fingerprint configuration for the profile"""
        
        # Start with profile's Camoufox config
        config = profile.get_camoufox_config()
        
        # Generate additional fingerprint data if needed
        if not config or len(config) < 5:  # Minimal config
            generated_config = self.fingerprint_generator.generate_fingerprint(
                os_preference=self._extract_os_from_user_agent(profile.user_agent),
                browser_preference="firefox"
            )
            config.update(generated_config)
        
        # Apply custom config overrides
        if custom_config:
            config.update(custom_config)
        
        # Add antidetect enhancements
        antidetect_config = self.antidetect_config.get_enhanced_config(config)
        config.update(antidetect_config)
        
        return config
    

    
    async def _get_context_config(
        self,
        profile: BrowserProfile,
        proxy: Optional[Proxy]
    ) -> Dict[str, Any]:
        """Get browser context configuration"""
        
        config = {
            "viewport": None,  # Let Camoufox handle viewport
            "ignore_https_errors": True,
            "java_script_enabled": True,
            "accept_downloads": True,
        }
        
        # Add user agent if specified
        if profile.user_agent:
            config["user_agent"] = profile.user_agent
        
        # Add geolocation if configured
        if profile.geolocation_config:
            geo_config = profile.geolocation_config
            if "latitude" in geo_config and "longitude" in geo_config:
                config["geolocation"] = {
                    "latitude": geo_config["latitude"],
                    "longitude": geo_config["longitude"]
                }
                config["permissions"] = ["geolocation"]
        
        # Add locale if configured
        if profile.locale:
            config["locale"] = profile.locale
        
        # Add timezone if configured
        if profile.timezone:
            config["timezone_id"] = profile.timezone
        
        return config
    
    async def _apply_antidetect_measures(
        self,
        context: BrowserContext,
        profile: BrowserProfile
    ):
        """Apply additional antidetect measures to the context"""
        
        try:
            # Add stealth scripts
            await context.add_init_script("""
                // Remove webdriver property
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
                
                // Override permissions
                const originalQuery = window.navigator.permissions.query;
                window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                        Promise.resolve({ state: Notification.permission }) :
                        originalQuery(parameters)
                );
                
                // Override plugins
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });
                
                // Override languages
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                });
            """)
            
            # Set additional headers
            await context.set_extra_http_headers({
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Language": "en-US,en;q=0.5",
                "Accept-Encoding": "gzip, deflate",
                "DNT": "1",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
            })
            
        except Exception as e:
            logger.warning(f"Failed to apply some antidetect measures: {e}")
    
    async def _create_user_data_dir(self, profile_id: int) -> str:
        """Create user data directory for the profile"""
        
        user_data_dir = settings.PROFILES_DIR / f"profile_{profile_id}"
        user_data_dir.mkdir(parents=True, exist_ok=True)
        
        return str(user_data_dir)
    
    def _extract_os_from_user_agent(self, user_agent: Optional[str]) -> str:
        """Extract OS preference from user agent"""
        
        if not user_agent:
            return "windows"
        
        user_agent_lower = user_agent.lower()
        
        if "windows" in user_agent_lower:
            return "windows"
        elif "mac" in user_agent_lower or "darwin" in user_agent_lower:
            return "macos"
        elif "linux" in user_agent_lower:
            return "linux"
        else:
            return "windows"  # Default
