{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/pages/Dashboard.jsx\",\n  _s = $RefreshSig$();\n/**\n * TikTok Automation Dashboard - Main management interface\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { FiPlus, FiPlay, FiPause, FiStopCircle } from 'react-icons/fi';\n\n// Components\nimport ProfileTable from '../components/Dashboard/ProfileTable';\nimport ProfileForm from '../components/Dashboard/ProfileForm';\nimport LogMonitor from '../components/Dashboard/LogMonitor';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  // State management\n  const [profiles, setProfiles] = useState([]);\n  const [showProfileForm, setShowProfileForm] = useState(false);\n  const [selectedProfiles, setSelectedProfiles] = useState([]);\n  const [automationSettings, setAutomationSettings] = useState({\n    targetProfile: '',\n    videosToWatch: 3,\n    watchTimeRange: '2000-5000',\n    followLimit: 10,\n    followDelay: '3000-8000'\n  });\n  const [logs, setLogs] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Mock data for development\n  useEffect(() => {\n    const mockProfiles = [{\n      id: 1,\n      stt: 1,\n      username: '<EMAIL>',\n      proxy: '*************:8080',\n      status: 'CHƯA ĐĂNG NHẬP',\n      followersFollowed: 0,\n      followersToday: 0,\n      currentAction: 'Chờ đăng nhập',\n      actions: ['login', 'complete']\n    }, {\n      id: 2,\n      stt: 2,\n      username: '<EMAIL>',\n      proxy: '192.168.1.101:8080',\n      status: 'SẴN SÀNG',\n      followersFollowed: 0,\n      followersToday: 0,\n      currentAction: 'Sẵn sàng chạy',\n      actions: ['start', 'stop']\n    }, {\n      id: 3,\n      stt: 3,\n      username: '<EMAIL>',\n      proxy: '192.168.1.102:8080',\n      status: 'ĐANG NHẬP HOẠT ĐỘ',\n      followersFollowed: 0,\n      followersToday: 0,\n      currentAction: 'Đang tương tác',\n      actions: ['pause', 'stop']\n    }];\n    const mockLogs = [{\n      id: 1,\n      time: '09:45:30',\n      level: 'INFO',\n      message: 'Profile 1 đã sẵn sàng cho account: 8f9d9c4b-4c4f-4c4f-8f9d-9c4b4c4f4c4f'\n    }, {\n      id: 2,\n      time: '09:45:31',\n      level: 'INFO',\n      message: 'Cookies saved for account: 8f9d9c4b-4c4f-4c4f-8f9d-9c4b4c4f4c4f'\n    }, {\n      id: 3,\n      time: '09:45:32',\n      level: 'INFO',\n      message: 'Cookies saved for account: 8f9d9c4b-4c4f-4c4f-8f9d-9c4b4c4f4c4f'\n    }, {\n      id: 4,\n      time: '09:45:33',\n      level: 'INFO',\n      message: 'Cookies saved for account: 8f9d9c4b-4c4f-4c4f-8f9d-9c4b4c4f4c4f'\n    }, {\n      id: 5,\n      time: '09:45:34',\n      level: 'INFO',\n      message: 'Cookies saved for account: 8f9d9c4b-4c4f-4c4f-8f9d-9c4b4c4f4c4f'\n    }];\n    setProfiles(mockProfiles);\n    setLogs(mockLogs);\n    setLoading(false);\n  }, []);\n\n  // Handle profile actions\n  const handleProfileAction = (profileId, action) => {\n    console.log(`Action ${action} for profile ${profileId}`);\n    // TODO: Implement actual API calls\n\n    // Update profile status based on action\n    setProfiles(prev => prev.map(profile => {\n      if (profile.id === profileId) {\n        switch (action) {\n          case 'login':\n            return {\n              ...profile,\n              status: 'ĐANG ĐĂNG NHẬP',\n              currentAction: 'Đang đăng nhập...'\n            };\n          case 'complete':\n            return {\n              ...profile,\n              status: 'SẴN SÀNG',\n              currentAction: 'Sẵn sàng chạy',\n              actions: ['start', 'stop']\n            };\n          case 'start':\n            return {\n              ...profile,\n              status: 'ĐANG NHẬP HOẠT ĐỘ',\n              currentAction: 'Đang tương tác',\n              actions: ['pause', 'stop']\n            };\n          case 'pause':\n            return {\n              ...profile,\n              status: 'TẠM DỪNG',\n              currentAction: 'Đã tạm dừng',\n              actions: ['start', 'stop']\n            };\n          case 'stop':\n            return {\n              ...profile,\n              status: 'SẴN SÀNG',\n              currentAction: 'Đã dừng',\n              actions: ['start']\n            };\n          default:\n            return profile;\n        }\n      }\n      return profile;\n    }));\n  };\n  const handleProfileSelect = (profileId, isSelected) => {\n    setSelectedProfiles(prev => {\n      if (isSelected) {\n        return [...prev, profileId];\n      } else {\n        return prev.filter(id => id !== profileId);\n      }\n    });\n  };\n  const handleSelectAll = isSelected => {\n    if (isSelected) {\n      setSelectedProfiles(profiles.map(p => p.id));\n    } else {\n      setSelectedProfiles([]);\n    }\n  };\n  const handleBulkAction = action => {\n    console.log(`Bulk action ${action} for profiles:`, selectedProfiles);\n    selectedProfiles.forEach(profileId => {\n      handleProfileAction(profileId, action);\n    });\n  };\n  const handleCreateProfile = profileData => {\n    console.log('Creating profile:', profileData);\n\n    // Add new profile to list\n    const newProfile = {\n      id: profiles.length + 1,\n      stt: profiles.length + 1,\n      username: profileData.profileName,\n      proxy: profileData.proxyType === 'no-proxy' ? 'Local Network' : `${profileData.host}:${profileData.port}`,\n      status: 'CHƯA ĐĂNG NHẬP',\n      followersFollowed: 0,\n      followersToday: 0,\n      currentAction: 'Chờ đăng nhập',\n      actions: ['login']\n    };\n    setProfiles(prev => [...prev, newProfile]);\n    setShowProfileForm(false);\n\n    // Add log entry\n    const newLog = {\n      id: logs.length + 1,\n      time: new Date().toLocaleTimeString(),\n      level: 'INFO',\n      message: `Profile \"${profileData.profileName}\" đã được tạo thành công`\n    };\n    setLogs(prev => [...prev, newLog]);\n  };\n  const handleClearLogs = () => {\n    setLogs([]);\n  };\n  const handleExportLogs = () => {\n    console.log('Exporting logs...');\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-purple-600 via-purple-700 to-purple-800\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/10 backdrop-blur-sm border-b border-white/20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-6 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-white\",\n            children: \"TikTok Automation Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 bg-green-500 px-3 py-1 rounded-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-2 h-2 bg-white rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white text-sm font-medium\",\n                children: \"Online\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-3 space-y-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 border-b border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-lg font-semibold text-gray-900\",\n                  children: \"Qu\\u1EA3n l\\xFD T\\xE0i kho\\u1EA3n (3)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setShowProfileForm(true),\n                    className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(FiPlus, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 212,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"T\\u1EA1o m\\u1EDBi\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 213,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleBulkAction('start'),\n                    className: \"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(FiPlay, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 219,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Ch\\u1EA1y t\\u1EA5t c\\u1EA3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 220,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleBulkAction('stop'),\n                    className: \"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(FiStopCircle, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 226,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"D\\u1EEBng t\\u1EA5t c\\u1EA3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 227,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ProfileTable, {\n              profiles: profiles,\n              selectedProfiles: selectedProfiles,\n              onProfileSelect: handleProfileSelect,\n              onProfileAction: handleProfileAction,\n              onSelectAll: handleSelectAll\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-sm p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4\",\n              children: \"\\u0110i\\u1EC1u khi\\u1EC3n & C\\xE0i \\u0111\\u1EB7t\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Ng\\xE0y \\u0111\\u1ED5i theo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"px-3 py-1 bg-blue-600 text-white rounded text-sm\",\n                    children: \"H\\xF4m nay\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 254,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm\",\n                    children: \"H\\xF4m qua\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 255,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm\",\n                    children: \"Tu\\u1EA7n n\\xE0y\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: [\"\\u0110i\\u1EC1u khi\\u1EC3n (\", selectedProfiles.length, \" t\\xE0i kho\\u1EA3n \\u0111ang ch\\u1ECDn)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleBulkAction('start'),\n                    disabled: selectedProfiles.length === 0,\n                    className: \"px-3 py-1 bg-green-600 text-white rounded text-sm flex items-center space-x-1 disabled:opacity-50 disabled:cursor-not-allowed\",\n                    children: [/*#__PURE__*/_jsxDEV(FiPlay, {\n                      className: \"w-3 h-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 270,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"B\\u1EAFt \\u0111\\u1EA7u\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 271,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleBulkAction('pause'),\n                    disabled: selectedProfiles.length === 0,\n                    className: \"px-3 py-1 bg-yellow-600 text-white rounded text-sm flex items-center space-x-1 disabled:opacity-50 disabled:cursor-not-allowed\",\n                    children: [/*#__PURE__*/_jsxDEV(FiPause, {\n                      className: \"w-3 h-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 278,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"T\\u1EA1m d\\u1EEBng\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 279,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleBulkAction('stop'),\n                    disabled: selectedProfiles.length === 0,\n                    className: \"px-3 py-1 bg-red-600 text-white rounded text-sm flex items-center space-x-1 disabled:opacity-50 disabled:cursor-not-allowed\",\n                    children: [/*#__PURE__*/_jsxDEV(FiStop, {\n                      className: \"w-3 h-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 286,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"D\\u1EEBng h\\u1EB3n\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 287,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"C\\xE0i \\u0111\\u1EB7t k\\u1ECBch b\\u1EA3n\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-xs text-gray-600 mb-1\",\n                      children: \"Link profile \\u0111\\u1ED1i th\\u1EE7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 296,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      placeholder: \"https://www.tiktok.com/@username\",\n                      className: \"w-full px-3 py-2 border border-gray-300 rounded text-sm\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 297,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-2 gap-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-xs text-gray-600 mb-1\",\n                        children: \"S\\u1ED1 video xem\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 306,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"number\",\n                        defaultValue: \"3\",\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded text-sm\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 307,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 305,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-xs text-gray-600 mb-1\",\n                        children: \"Th\\u1EDDi gian xem (gi\\xE2y)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 310,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        defaultValue: \"30\",\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded text-sm\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 311,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 309,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-2 gap-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-xs text-gray-600 mb-1\",\n                        children: \"Follow t\\u1ED1i \\u0111a/ng\\xE0y\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 317,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"number\",\n                        defaultValue: \"10\",\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded text-sm\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 318,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 316,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-xs text-gray-600 mb-1\",\n                        children: \"Kho\\u1EA3ng c\\xE1ch gi\\u1EEFa phi\\xEAn (gi\\xE2y)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 321,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        defaultValue: \"3600\",\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded text-sm\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 322,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 320,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 transition-colors\",\n                    children: \"C\\u1EADp nh\\u1EADt c\\xE0i \\u0111\\u1EB7t\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 326,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(LogMonitor, {\n            logs: logs,\n            onClearLogs: handleClearLogs,\n            onExportLogs: handleExportLogs\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ProfileForm, {\n      isOpen: showProfileForm,\n      onClose: () => setShowProfileForm(false),\n      onSubmit: handleCreateProfile\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 182,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"nmTYStw3nBMO7fsxrNUml+M5nsY=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FiPlus", "FiPlay", "FiPause", "FiStopCircle", "ProfileTable", "ProfileForm", "LogMonitor", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "profiles", "setProfiles", "showProfileForm", "setShowProfileForm", "selected<PERSON><PERSON><PERSON><PERSON>", "setSelectedProfiles", "automationSettings", "setAutomationSettings", "targetProfile", "videosToWatch", "watchTimeRange", "followLimit", "<PERSON><PERSON><PERSON><PERSON>", "logs", "setLogs", "loading", "setLoading", "mockProfiles", "id", "stt", "username", "proxy", "status", "followersFollowed", "followers<PERSON>oday", "currentAction", "actions", "mockLogs", "time", "level", "message", "handleProfileAction", "profileId", "action", "console", "log", "prev", "map", "profile", "handleProfileSelect", "isSelected", "filter", "handleSelectAll", "p", "handleBulkAction", "for<PERSON>ach", "handleCreateProfile", "profileData", "newProfile", "length", "profileName", "proxyType", "host", "port", "newLog", "Date", "toLocaleTimeString", "handleClearLogs", "handleExportLogs", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onProfileSelect", "onProfileAction", "onSelectAll", "disabled", "FiStop", "type", "placeholder", "defaultValue", "onClearLogs", "onExportLogs", "isOpen", "onClose", "onSubmit", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/pages/Dashboard.jsx"], "sourcesContent": ["/**\n * TikTok Automation Dashboard - Main management interface\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  FiPlus,\n  FiPlay,\n  FiPause,\n  FiStopCircle\n} from 'react-icons/fi';\n\n// Components\nimport ProfileTable from '../components/Dashboard/ProfileTable';\nimport ProfileForm from '../components/Dashboard/ProfileForm';\nimport LogMonitor from '../components/Dashboard/LogMonitor';\n\nconst Dashboard = () => {\n  // State management\n  const [profiles, setProfiles] = useState([]);\n  const [showProfileForm, setShowProfileForm] = useState(false);\n  const [selectedProfiles, setSelectedProfiles] = useState([]);\n  const [automationSettings, setAutomationSettings] = useState({\n    targetProfile: '',\n    videosToWatch: 3,\n    watchTimeRange: '2000-5000',\n    followLimit: 10,\n    followDelay: '3000-8000'\n  });\n  const [logs, setLogs] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Mock data for development\n  useEffect(() => {\n    const mockProfiles = [\n      {\n        id: 1,\n        stt: 1,\n        username: '<EMAIL>',\n        proxy: '*************:8080',\n        status: 'CHƯA ĐĂNG NHẬP',\n        followersFollowed: 0,\n        followersToday: 0,\n        currentAction: 'Chờ đăng nhập',\n        actions: ['login', 'complete']\n      },\n      {\n        id: 2,\n        stt: 2,\n        username: '<EMAIL>',\n        proxy: '192.168.1.101:8080',\n        status: 'SẴN SÀNG',\n        followersFollowed: 0,\n        followersToday: 0,\n        currentAction: 'Sẵn sàng chạy',\n        actions: ['start', 'stop']\n      },\n      {\n        id: 3,\n        stt: 3,\n        username: '<EMAIL>',\n        proxy: '192.168.1.102:8080',\n        status: 'ĐANG NHẬP HOẠT ĐỘ',\n        followersFollowed: 0,\n        followersToday: 0,\n        currentAction: 'Đang tương tác',\n        actions: ['pause', 'stop']\n      }\n    ];\n\n    const mockLogs = [\n      { id: 1, time: '09:45:30', level: 'INFO', message: 'Profile 1 đã sẵn sàng cho account: 8f9d9c4b-4c4f-4c4f-8f9d-9c4b4c4f4c4f' },\n      { id: 2, time: '09:45:31', level: 'INFO', message: 'Cookies saved for account: 8f9d9c4b-4c4f-4c4f-8f9d-9c4b4c4f4c4f' },\n      { id: 3, time: '09:45:32', level: 'INFO', message: 'Cookies saved for account: 8f9d9c4b-4c4f-4c4f-8f9d-9c4b4c4f4c4f' },\n      { id: 4, time: '09:45:33', level: 'INFO', message: 'Cookies saved for account: 8f9d9c4b-4c4f-4c4f-8f9d-9c4b4c4f4c4f' },\n      { id: 5, time: '09:45:34', level: 'INFO', message: 'Cookies saved for account: 8f9d9c4b-4c4f-4c4f-8f9d-9c4b4c4f4c4f' }\n    ];\n\n    setProfiles(mockProfiles);\n    setLogs(mockLogs);\n    setLoading(false);\n  }, []);\n\n  // Handle profile actions\n  const handleProfileAction = (profileId, action) => {\n    console.log(`Action ${action} for profile ${profileId}`);\n    // TODO: Implement actual API calls\n\n    // Update profile status based on action\n    setProfiles(prev => prev.map(profile => {\n      if (profile.id === profileId) {\n        switch (action) {\n          case 'login':\n            return { ...profile, status: 'ĐANG ĐĂNG NHẬP', currentAction: 'Đang đăng nhập...' };\n          case 'complete':\n            return { ...profile, status: 'SẴN SÀNG', currentAction: 'Sẵn sàng chạy', actions: ['start', 'stop'] };\n          case 'start':\n            return { ...profile, status: 'ĐANG NHẬP HOẠT ĐỘ', currentAction: 'Đang tương tác', actions: ['pause', 'stop'] };\n          case 'pause':\n            return { ...profile, status: 'TẠM DỪNG', currentAction: 'Đã tạm dừng', actions: ['start', 'stop'] };\n          case 'stop':\n            return { ...profile, status: 'SẴN SÀNG', currentAction: 'Đã dừng', actions: ['start'] };\n          default:\n            return profile;\n        }\n      }\n      return profile;\n    }));\n  };\n\n  const handleProfileSelect = (profileId, isSelected) => {\n    setSelectedProfiles(prev => {\n      if (isSelected) {\n        return [...prev, profileId];\n      } else {\n        return prev.filter(id => id !== profileId);\n      }\n    });\n  };\n\n  const handleSelectAll = (isSelected) => {\n    if (isSelected) {\n      setSelectedProfiles(profiles.map(p => p.id));\n    } else {\n      setSelectedProfiles([]);\n    }\n  };\n\n  const handleBulkAction = (action) => {\n    console.log(`Bulk action ${action} for profiles:`, selectedProfiles);\n    selectedProfiles.forEach(profileId => {\n      handleProfileAction(profileId, action);\n    });\n  };\n\n  const handleCreateProfile = (profileData) => {\n    console.log('Creating profile:', profileData);\n\n    // Add new profile to list\n    const newProfile = {\n      id: profiles.length + 1,\n      stt: profiles.length + 1,\n      username: profileData.profileName,\n      proxy: profileData.proxyType === 'no-proxy' ? 'Local Network' : `${profileData.host}:${profileData.port}`,\n      status: 'CHƯA ĐĂNG NHẬP',\n      followersFollowed: 0,\n      followersToday: 0,\n      currentAction: 'Chờ đăng nhập',\n      actions: ['login']\n    };\n\n    setProfiles(prev => [...prev, newProfile]);\n    setShowProfileForm(false);\n\n    // Add log entry\n    const newLog = {\n      id: logs.length + 1,\n      time: new Date().toLocaleTimeString(),\n      level: 'INFO',\n      message: `Profile \"${profileData.profileName}\" đã được tạo thành công`\n    };\n    setLogs(prev => [...prev, newLog]);\n  };\n\n  const handleClearLogs = () => {\n    setLogs([]);\n  };\n\n  const handleExportLogs = () => {\n    console.log('Exporting logs...');\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-purple-600 via-purple-700 to-purple-800\">\n      {/* Header */}\n      <div className=\"bg-white/10 backdrop-blur-sm border-b border-white/20\">\n        <div className=\"px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <h1 className=\"text-2xl font-bold text-white\">TikTok Automation Dashboard</h1>\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"flex items-center space-x-2 bg-green-500 px-3 py-1 rounded-full\">\n                <div className=\"w-2 h-2 bg-white rounded-full\"></div>\n                <span className=\"text-white text-sm font-medium\">Online</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"p-6\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6\">\n          {/* Left Panel - Profile Management */}\n          <div className=\"lg:col-span-3 space-y-6\">\n            {/* Profile Controls */}\n            <div className=\"bg-white rounded-lg shadow-sm\">\n              <div className=\"p-4 border-b border-gray-200\">\n                <div className=\"flex items-center justify-between\">\n                  <h2 className=\"text-lg font-semibold text-gray-900\">Quản lý Tài khoản (3)</h2>\n                  <div className=\"flex items-center space-x-2\">\n                    <button\n                      onClick={() => setShowProfileForm(true)}\n                      className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2\"\n                    >\n                      <FiPlus className=\"w-4 h-4\" />\n                      <span>Tạo mới</span>\n                    </button>\n                    <button\n                      onClick={() => handleBulkAction('start')}\n                      className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2\"\n                    >\n                      <FiPlay className=\"w-4 h-4\" />\n                      <span>Chạy tất cả</span>\n                    </button>\n                    <button\n                      onClick={() => handleBulkAction('stop')}\n                      className=\"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2\"\n                    >\n                      <FiStopCircle className=\"w-4 h-4\" />\n                      <span>Dừng tất cả</span>\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              {/* Profile Table */}\n              <ProfileTable\n                profiles={profiles}\n                selectedProfiles={selectedProfiles}\n                onProfileSelect={handleProfileSelect}\n                onProfileAction={handleProfileAction}\n                onSelectAll={handleSelectAll}\n              />\n            </div>\n          </div>\n\n          {/* Right Panel - Settings and Logs */}\n          <div className=\"space-y-6\">\n            {/* Automation Settings */}\n            <div className=\"bg-white rounded-lg shadow-sm p-4\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Điều khiển & Cài đặt</h3>\n\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Ngày đổi theo</label>\n                  <div className=\"flex space-x-2\">\n                    <button className=\"px-3 py-1 bg-blue-600 text-white rounded text-sm\">Hôm nay</button>\n                    <button className=\"px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm\">Hôm qua</button>\n                    <button className=\"px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm\">Tuần này</button>\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Điều khiển ({selectedProfiles.length} tài khoản đang chọn)\n                  </label>\n                  <div className=\"flex space-x-2\">\n                    <button\n                      onClick={() => handleBulkAction('start')}\n                      disabled={selectedProfiles.length === 0}\n                      className=\"px-3 py-1 bg-green-600 text-white rounded text-sm flex items-center space-x-1 disabled:opacity-50 disabled:cursor-not-allowed\"\n                    >\n                      <FiPlay className=\"w-3 h-3\" />\n                      <span>Bắt đầu</span>\n                    </button>\n                    <button\n                      onClick={() => handleBulkAction('pause')}\n                      disabled={selectedProfiles.length === 0}\n                      className=\"px-3 py-1 bg-yellow-600 text-white rounded text-sm flex items-center space-x-1 disabled:opacity-50 disabled:cursor-not-allowed\"\n                    >\n                      <FiPause className=\"w-3 h-3\" />\n                      <span>Tạm dừng</span>\n                    </button>\n                    <button\n                      onClick={() => handleBulkAction('stop')}\n                      disabled={selectedProfiles.length === 0}\n                      className=\"px-3 py-1 bg-red-600 text-white rounded text-sm flex items-center space-x-1 disabled:opacity-50 disabled:cursor-not-allowed\"\n                    >\n                      <FiStop className=\"w-3 h-3\" />\n                      <span>Dừng hẳn</span>\n                    </button>\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Cài đặt kịch bản</label>\n                  <div className=\"space-y-3\">\n                    <div>\n                      <label className=\"block text-xs text-gray-600 mb-1\">Link profile đối thủ</label>\n                      <input\n                        type=\"text\"\n                        placeholder=\"https://www.tiktok.com/@username\"\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded text-sm\"\n                      />\n                    </div>\n\n                    <div className=\"grid grid-cols-2 gap-2\">\n                      <div>\n                        <label className=\"block text-xs text-gray-600 mb-1\">Số video xem</label>\n                        <input type=\"number\" defaultValue=\"3\" className=\"w-full px-3 py-2 border border-gray-300 rounded text-sm\" />\n                      </div>\n                      <div>\n                        <label className=\"block text-xs text-gray-600 mb-1\">Thời gian xem (giây)</label>\n                        <input type=\"text\" defaultValue=\"30\" className=\"w-full px-3 py-2 border border-gray-300 rounded text-sm\" />\n                      </div>\n                    </div>\n\n                    <div className=\"grid grid-cols-2 gap-2\">\n                      <div>\n                        <label className=\"block text-xs text-gray-600 mb-1\">Follow tối đa/ngày</label>\n                        <input type=\"number\" defaultValue=\"10\" className=\"w-full px-3 py-2 border border-gray-300 rounded text-sm\" />\n                      </div>\n                      <div>\n                        <label className=\"block text-xs text-gray-600 mb-1\">Khoảng cách giữa phiên (giây)</label>\n                        <input type=\"text\" defaultValue=\"3600\" className=\"w-full px-3 py-2 border border-gray-300 rounded text-sm\" />\n                      </div>\n                    </div>\n\n                    <button className=\"w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 transition-colors\">\n                      Cập nhật cài đặt\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Log Monitor */}\n            <LogMonitor\n              logs={logs}\n              onClearLogs={handleClearLogs}\n              onExportLogs={handleExportLogs}\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Profile Form Modal */}\n      <ProfileForm\n        isOpen={showProfileForm}\n        onClose={() => setShowProfileForm(false)}\n        onSubmit={handleCreateProfile}\n      />\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,YAAY,QACP,gBAAgB;;AAEvB;AACA,OAAOC,YAAY,MAAM,sCAAsC;AAC/D,OAAOC,WAAW,MAAM,qCAAqC;AAC7D,OAAOC,UAAU,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACe,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACiB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACmB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpB,QAAQ,CAAC;IAC3DqB,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE,WAAW;IAC3BC,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,MAAM6B,YAAY,GAAG,CACnB;MACEC,EAAE,EAAE,CAAC;MACLC,GAAG,EAAE,CAAC;MACNC,QAAQ,EAAE,iBAAiB;MAC3BC,KAAK,EAAE,oBAAoB;MAC3BC,MAAM,EAAE,gBAAgB;MACxBC,iBAAiB,EAAE,CAAC;MACpBC,cAAc,EAAE,CAAC;MACjBC,aAAa,EAAE,eAAe;MAC9BC,OAAO,EAAE,CAAC,OAAO,EAAE,UAAU;IAC/B,CAAC,EACD;MACER,EAAE,EAAE,CAAC;MACLC,GAAG,EAAE,CAAC;MACNC,QAAQ,EAAE,iBAAiB;MAC3BC,KAAK,EAAE,oBAAoB;MAC3BC,MAAM,EAAE,UAAU;MAClBC,iBAAiB,EAAE,CAAC;MACpBC,cAAc,EAAE,CAAC;MACjBC,aAAa,EAAE,eAAe;MAC9BC,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM;IAC3B,CAAC,EACD;MACER,EAAE,EAAE,CAAC;MACLC,GAAG,EAAE,CAAC;MACNC,QAAQ,EAAE,iBAAiB;MAC3BC,KAAK,EAAE,oBAAoB;MAC3BC,MAAM,EAAE,mBAAmB;MAC3BC,iBAAiB,EAAE,CAAC;MACpBC,cAAc,EAAE,CAAC;MACjBC,aAAa,EAAE,gBAAgB;MAC/BC,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM;IAC3B,CAAC,CACF;IAED,MAAMC,QAAQ,GAAG,CACf;MAAET,EAAE,EAAE,CAAC;MAAEU,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE,MAAM;MAAEC,OAAO,EAAE;IAA0E,CAAC,EAC9H;MAAEZ,EAAE,EAAE,CAAC;MAAEU,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE,MAAM;MAAEC,OAAO,EAAE;IAAkE,CAAC,EACtH;MAAEZ,EAAE,EAAE,CAAC;MAAEU,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE,MAAM;MAAEC,OAAO,EAAE;IAAkE,CAAC,EACtH;MAAEZ,EAAE,EAAE,CAAC;MAAEU,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE,MAAM;MAAEC,OAAO,EAAE;IAAkE,CAAC,EACtH;MAAEZ,EAAE,EAAE,CAAC;MAAEU,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE,MAAM;MAAEC,OAAO,EAAE;IAAkE,CAAC,CACvH;IAED7B,WAAW,CAACgB,YAAY,CAAC;IACzBH,OAAO,CAACa,QAAQ,CAAC;IACjBX,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMe,mBAAmB,GAAGA,CAACC,SAAS,EAAEC,MAAM,KAAK;IACjDC,OAAO,CAACC,GAAG,CAAC,UAAUF,MAAM,gBAAgBD,SAAS,EAAE,CAAC;IACxD;;IAEA;IACA/B,WAAW,CAACmC,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,OAAO,IAAI;MACtC,IAAIA,OAAO,CAACpB,EAAE,KAAKc,SAAS,EAAE;QAC5B,QAAQC,MAAM;UACZ,KAAK,OAAO;YACV,OAAO;cAAE,GAAGK,OAAO;cAAEhB,MAAM,EAAE,gBAAgB;cAAEG,aAAa,EAAE;YAAoB,CAAC;UACrF,KAAK,UAAU;YACb,OAAO;cAAE,GAAGa,OAAO;cAAEhB,MAAM,EAAE,UAAU;cAAEG,aAAa,EAAE,eAAe;cAAEC,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM;YAAE,CAAC;UACvG,KAAK,OAAO;YACV,OAAO;cAAE,GAAGY,OAAO;cAAEhB,MAAM,EAAE,mBAAmB;cAAEG,aAAa,EAAE,gBAAgB;cAAEC,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM;YAAE,CAAC;UACjH,KAAK,OAAO;YACV,OAAO;cAAE,GAAGY,OAAO;cAAEhB,MAAM,EAAE,UAAU;cAAEG,aAAa,EAAE,aAAa;cAAEC,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM;YAAE,CAAC;UACrG,KAAK,MAAM;YACT,OAAO;cAAE,GAAGY,OAAO;cAAEhB,MAAM,EAAE,UAAU;cAAEG,aAAa,EAAE,SAAS;cAAEC,OAAO,EAAE,CAAC,OAAO;YAAE,CAAC;UACzF;YACE,OAAOY,OAAO;QAClB;MACF;MACA,OAAOA,OAAO;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAACP,SAAS,EAAEQ,UAAU,KAAK;IACrDnC,mBAAmB,CAAC+B,IAAI,IAAI;MAC1B,IAAII,UAAU,EAAE;QACd,OAAO,CAAC,GAAGJ,IAAI,EAAEJ,SAAS,CAAC;MAC7B,CAAC,MAAM;QACL,OAAOI,IAAI,CAACK,MAAM,CAACvB,EAAE,IAAIA,EAAE,KAAKc,SAAS,CAAC;MAC5C;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMU,eAAe,GAAIF,UAAU,IAAK;IACtC,IAAIA,UAAU,EAAE;MACdnC,mBAAmB,CAACL,QAAQ,CAACqC,GAAG,CAACM,CAAC,IAAIA,CAAC,CAACzB,EAAE,CAAC,CAAC;IAC9C,CAAC,MAAM;MACLb,mBAAmB,CAAC,EAAE,CAAC;IACzB;EACF,CAAC;EAED,MAAMuC,gBAAgB,GAAIX,MAAM,IAAK;IACnCC,OAAO,CAACC,GAAG,CAAC,eAAeF,MAAM,gBAAgB,EAAE7B,gBAAgB,CAAC;IACpEA,gBAAgB,CAACyC,OAAO,CAACb,SAAS,IAAI;MACpCD,mBAAmB,CAACC,SAAS,EAAEC,MAAM,CAAC;IACxC,CAAC,CAAC;EACJ,CAAC;EAED,MAAMa,mBAAmB,GAAIC,WAAW,IAAK;IAC3Cb,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEY,WAAW,CAAC;;IAE7C;IACA,MAAMC,UAAU,GAAG;MACjB9B,EAAE,EAAElB,QAAQ,CAACiD,MAAM,GAAG,CAAC;MACvB9B,GAAG,EAAEnB,QAAQ,CAACiD,MAAM,GAAG,CAAC;MACxB7B,QAAQ,EAAE2B,WAAW,CAACG,WAAW;MACjC7B,KAAK,EAAE0B,WAAW,CAACI,SAAS,KAAK,UAAU,GAAG,eAAe,GAAG,GAAGJ,WAAW,CAACK,IAAI,IAAIL,WAAW,CAACM,IAAI,EAAE;MACzG/B,MAAM,EAAE,gBAAgB;MACxBC,iBAAiB,EAAE,CAAC;MACpBC,cAAc,EAAE,CAAC;MACjBC,aAAa,EAAE,eAAe;MAC9BC,OAAO,EAAE,CAAC,OAAO;IACnB,CAAC;IAEDzB,WAAW,CAACmC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEY,UAAU,CAAC,CAAC;IAC1C7C,kBAAkB,CAAC,KAAK,CAAC;;IAEzB;IACA,MAAMmD,MAAM,GAAG;MACbpC,EAAE,EAAEL,IAAI,CAACoC,MAAM,GAAG,CAAC;MACnBrB,IAAI,EAAE,IAAI2B,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;MACrC3B,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,YAAYiB,WAAW,CAACG,WAAW;IAC9C,CAAC;IACDpC,OAAO,CAACsB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEkB,MAAM,CAAC,CAAC;EACpC,CAAC;EAED,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5B3C,OAAO,CAAC,EAAE,CAAC;EACb,CAAC;EAED,MAAM4C,gBAAgB,GAAGA,CAAA,KAAM;IAC7BxB,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;EAClC,CAAC;EAED,IAAIpB,OAAO,EAAE;IACX,oBACElB,OAAA;MAAK8D,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpD/D,OAAA;QAAK8D,SAAS,EAAC;MAAgE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC;EAEV;EAEA,oBACEnE,OAAA;IAAK8D,SAAS,EAAC,6EAA6E;IAAAC,QAAA,gBAE1F/D,OAAA;MAAK8D,SAAS,EAAC,uDAAuD;MAAAC,QAAA,eACpE/D,OAAA;QAAK8D,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB/D,OAAA;UAAK8D,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD/D,OAAA;YAAI8D,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAAC;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9EnE,OAAA;YAAK8D,SAAS,EAAC,6BAA6B;YAAAC,QAAA,eAC1C/D,OAAA;cAAK8D,SAAS,EAAC,iEAAiE;cAAAC,QAAA,gBAC9E/D,OAAA;gBAAK8D,SAAS,EAAC;cAA+B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrDnE,OAAA;gBAAM8D,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENnE,OAAA;MAAK8D,SAAS,EAAC,KAAK;MAAAC,QAAA,eAClB/D,OAAA;QAAK8D,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpD/D,OAAA;UAAK8D,SAAS,EAAC,yBAAyB;UAAAC,QAAA,eAEtC/D,OAAA;YAAK8D,SAAS,EAAC,+BAA+B;YAAAC,QAAA,gBAC5C/D,OAAA;cAAK8D,SAAS,EAAC,8BAA8B;cAAAC,QAAA,eAC3C/D,OAAA;gBAAK8D,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChD/D,OAAA;kBAAI8D,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9EnE,OAAA;kBAAK8D,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C/D,OAAA;oBACEoE,OAAO,EAAEA,CAAA,KAAM9D,kBAAkB,CAAC,IAAI,CAAE;oBACxCwD,SAAS,EAAC,6GAA6G;oBAAAC,QAAA,gBAEvH/D,OAAA,CAACR,MAAM;sBAACsE,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC9BnE,OAAA;sBAAA+D,QAAA,EAAM;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC,eACTnE,OAAA;oBACEoE,OAAO,EAAEA,CAAA,KAAMrB,gBAAgB,CAAC,OAAO,CAAE;oBACzCe,SAAS,EAAC,+GAA+G;oBAAAC,QAAA,gBAEzH/D,OAAA,CAACP,MAAM;sBAACqE,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC9BnE,OAAA;sBAAA+D,QAAA,EAAM;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACTnE,OAAA;oBACEoE,OAAO,EAAEA,CAAA,KAAMrB,gBAAgB,CAAC,MAAM,CAAE;oBACxCe,SAAS,EAAC,2GAA2G;oBAAAC,QAAA,gBAErH/D,OAAA,CAACL,YAAY;sBAACmE,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACpCnE,OAAA;sBAAA+D,QAAA,EAAM;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNnE,OAAA,CAACJ,YAAY;cACXO,QAAQ,EAAEA,QAAS;cACnBI,gBAAgB,EAAEA,gBAAiB;cACnC8D,eAAe,EAAE3B,mBAAoB;cACrC4B,eAAe,EAAEpC,mBAAoB;cACrCqC,WAAW,EAAE1B;YAAgB;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnE,OAAA;UAAK8D,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExB/D,OAAA;YAAK8D,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD/D,OAAA;cAAI8D,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAElFnE,OAAA;cAAK8D,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB/D,OAAA;gBAAA+D,QAAA,gBACE/D,OAAA;kBAAO8D,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrFnE,OAAA;kBAAK8D,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7B/D,OAAA;oBAAQ8D,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACrFnE,OAAA;oBAAQ8D,SAAS,EAAC,qDAAqD;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxFnE,OAAA;oBAAQ8D,SAAS,EAAC,qDAAqD;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENnE,OAAA;gBAAA+D,QAAA,gBACE/D,OAAA;kBAAO8D,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,GAAC,6BAClD,EAACxD,gBAAgB,CAAC6C,MAAM,EAAC,yCACvC;gBAAA;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRnE,OAAA;kBAAK8D,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7B/D,OAAA;oBACEoE,OAAO,EAAEA,CAAA,KAAMrB,gBAAgB,CAAC,OAAO,CAAE;oBACzCyB,QAAQ,EAAEjE,gBAAgB,CAAC6C,MAAM,KAAK,CAAE;oBACxCU,SAAS,EAAC,+HAA+H;oBAAAC,QAAA,gBAEzI/D,OAAA,CAACP,MAAM;sBAACqE,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC9BnE,OAAA;sBAAA+D,QAAA,EAAM;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC,eACTnE,OAAA;oBACEoE,OAAO,EAAEA,CAAA,KAAMrB,gBAAgB,CAAC,OAAO,CAAE;oBACzCyB,QAAQ,EAAEjE,gBAAgB,CAAC6C,MAAM,KAAK,CAAE;oBACxCU,SAAS,EAAC,gIAAgI;oBAAAC,QAAA,gBAE1I/D,OAAA,CAACN,OAAO;sBAACoE,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC/BnE,OAAA;sBAAA+D,QAAA,EAAM;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,eACTnE,OAAA;oBACEoE,OAAO,EAAEA,CAAA,KAAMrB,gBAAgB,CAAC,MAAM,CAAE;oBACxCyB,QAAQ,EAAEjE,gBAAgB,CAAC6C,MAAM,KAAK,CAAE;oBACxCU,SAAS,EAAC,6HAA6H;oBAAAC,QAAA,gBAEvI/D,OAAA,CAACyE,MAAM;sBAACX,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC9BnE,OAAA;sBAAA+D,QAAA,EAAM;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENnE,OAAA;gBAAA+D,QAAA,gBACE/D,OAAA;kBAAO8D,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxFnE,OAAA;kBAAK8D,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxB/D,OAAA;oBAAA+D,QAAA,gBACE/D,OAAA;sBAAO8D,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAChFnE,OAAA;sBACE0E,IAAI,EAAC,MAAM;sBACXC,WAAW,EAAC,kCAAkC;sBAC9Cb,SAAS,EAAC;oBAAyD;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eAENnE,OAAA;oBAAK8D,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrC/D,OAAA;sBAAA+D,QAAA,gBACE/D,OAAA;wBAAO8D,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAC;sBAAY;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACxEnE,OAAA;wBAAO0E,IAAI,EAAC,QAAQ;wBAACE,YAAY,EAAC,GAAG;wBAACd,SAAS,EAAC;sBAAyD;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzG,CAAC,eACNnE,OAAA;sBAAA+D,QAAA,gBACE/D,OAAA;wBAAO8D,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAC;sBAAoB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAChFnE,OAAA;wBAAO0E,IAAI,EAAC,MAAM;wBAACE,YAAY,EAAC,IAAI;wBAACd,SAAS,EAAC;sBAAyD;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENnE,OAAA;oBAAK8D,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrC/D,OAAA;sBAAA+D,QAAA,gBACE/D,OAAA;wBAAO8D,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAC;sBAAkB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC9EnE,OAAA;wBAAO0E,IAAI,EAAC,QAAQ;wBAACE,YAAY,EAAC,IAAI;wBAACd,SAAS,EAAC;sBAAyD;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1G,CAAC,eACNnE,OAAA;sBAAA+D,QAAA,gBACE/D,OAAA;wBAAO8D,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,EAAC;sBAA6B;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACzFnE,OAAA;wBAAO0E,IAAI,EAAC,MAAM;wBAACE,YAAY,EAAC,MAAM;wBAACd,SAAS,EAAC;sBAAyD;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1G,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENnE,OAAA;oBAAQ8D,SAAS,EAAC,gFAAgF;oBAAAC,QAAA,EAAC;kBAEnG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNnE,OAAA,CAACF,UAAU;YACTkB,IAAI,EAAEA,IAAK;YACX6D,WAAW,EAAEjB,eAAgB;YAC7BkB,YAAY,EAAEjB;UAAiB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnE,OAAA,CAACH,WAAW;MACVkF,MAAM,EAAE1E,eAAgB;MACxB2E,OAAO,EAAEA,CAAA,KAAM1E,kBAAkB,CAAC,KAAK,CAAE;MACzC2E,QAAQ,EAAEhC;IAAoB;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACjE,EAAA,CA9UID,SAAS;AAAAiF,EAAA,GAATjF,SAAS;AAgVf,eAAeA,SAAS;AAAC,IAAAiF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}