{"ast": null, "code": "/**\n * Profile Service - Manages browser profiles and proxy integration\n */\n\nimport { profileAPI, proxyAPI, handleApiError } from './api';\nclass ProfileService {\n  constructor() {\n    this.profiles = [];\n    this.proxies = [];\n    this.listeners = [];\n  }\n\n  // Event listener management\n  addListener(callback) {\n    this.listeners.push(callback);\n  }\n  removeListener(callback) {\n    this.listeners = this.listeners.filter(l => l !== callback);\n  }\n  notifyListeners(event, data) {\n    this.listeners.forEach(listener => {\n      try {\n        listener(event, data);\n      } catch (error) {\n        console.error('Error in profile service listener:', error);\n      }\n    });\n  }\n\n  // Profile management\n  async loadProfiles() {\n    try {\n      const profiles = await profileAPI.getProfiles();\n      this.profiles = profiles.map(profile => this.transformProfileFromAPI(profile));\n      this.notifyListeners('profiles_loaded', this.profiles);\n      return this.profiles;\n    } catch (error) {\n      const apiError = handleApiError(error);\n      console.error('Failed to load profiles:', apiError);\n      throw new Error(apiError.message);\n    }\n  }\n  async createProfile(profileData) {\n    try {\n      // First create proxy if needed\n      let proxyId = null;\n      if (profileData.proxyType !== 'no-proxy') {\n        const proxy = await this.createProxyFromForm(profileData);\n        proxyId = proxy.id;\n      }\n\n      // Create profile\n      const apiProfileData = {\n        name: profileData.profileName,\n        description: `Profile with ${profileData.proxyType} proxy`,\n        proxy_id: proxyId,\n        auto_generate_fingerprint: true,\n        os_preference: 'windows',\n        browser_preference: 'firefox'\n      };\n      const newProfile = await profileAPI.createProfile(apiProfileData);\n      const transformedProfile = this.transformProfileFromAPI(newProfile);\n      this.profiles.push(transformedProfile);\n      this.notifyListeners('profile_created', transformedProfile);\n      return transformedProfile;\n    } catch (error) {\n      const apiError = handleApiError(error);\n      console.error('Failed to create profile:', apiError);\n      throw new Error(apiError.message);\n    }\n  }\n  async updateProfile(profileId, updates) {\n    try {\n      const updatedProfile = await profileAPI.updateProfile(profileId, updates);\n      const transformedProfile = this.transformProfileFromAPI(updatedProfile);\n      const index = this.profiles.findIndex(p => p.id === profileId);\n      if (index !== -1) {\n        this.profiles[index] = transformedProfile;\n        this.notifyListeners('profile_updated', transformedProfile);\n      }\n      return transformedProfile;\n    } catch (error) {\n      const apiError = handleApiError(error);\n      console.error('Failed to update profile:', apiError);\n      throw new Error(apiError.message);\n    }\n  }\n  async deleteProfile(profileId) {\n    try {\n      await profileAPI.deleteProfile(profileId);\n      this.profiles = this.profiles.filter(p => p.id !== profileId);\n      this.notifyListeners('profile_deleted', profileId);\n      return true;\n    } catch (error) {\n      const apiError = handleApiError(error);\n      console.error('Failed to delete profile:', apiError);\n      throw new Error(apiError.message);\n    }\n  }\n  async testProfile(profileId) {\n    try {\n      const result = await profileAPI.testProfile(profileId);\n      this.notifyListeners('profile_tested', {\n        profileId,\n        result\n      });\n      return result;\n    } catch (error) {\n      const apiError = handleApiError(error);\n      console.error('Failed to test profile:', apiError);\n      throw new Error(apiError.message);\n    }\n  }\n\n  // Proxy management\n  async createProxyFromForm(formData) {\n    try {\n      const proxyData = {\n        name: `${formData.profileName}_proxy`,\n        proxy_type: formData.proxyType,\n        host: formData.host,\n        port: parseInt(formData.port),\n        username: formData.username || null,\n        password: formData.password || null,\n        description: `Proxy for profile ${formData.profileName}`,\n        validate_on_create: true\n      };\n      const proxy = await proxyAPI.createProxy(proxyData);\n      return proxy;\n    } catch (error) {\n      const apiError = handleApiError(error);\n      console.error('Failed to create proxy:', apiError);\n      throw new Error(apiError.message);\n    }\n  }\n  async validateProxy(proxyData) {\n    try {\n      // Create temporary proxy for validation\n      const tempProxy = await this.createProxyFromForm({\n        ...proxyData,\n        profileName: 'temp_validation'\n      });\n\n      // Validate the proxy\n      const result = await proxyAPI.validateProxy(tempProxy.id);\n\n      // Clean up temporary proxy\n      await proxyAPI.deleteProxy(tempProxy.id);\n      return result;\n    } catch (error) {\n      const apiError = handleApiError(error);\n      console.error('Failed to validate proxy:', apiError);\n      throw new Error(apiError.message);\n    }\n  }\n\n  // Profile actions\n  async startLogin(profileId) {\n    try {\n      // Update profile status\n      await this.updateProfile(profileId, {\n        status: 'logging_in',\n        current_action: 'Đang đăng nhập...'\n      });\n\n      // TODO: Implement actual browser launch for login\n      this.notifyListeners('login_started', profileId);\n      return {\n        success: true,\n        message: 'Login process started'\n      };\n    } catch (error) {\n      console.error('Failed to start login:', error);\n      throw error;\n    }\n  }\n  async completeLogin(profileId) {\n    try {\n      // Update profile status\n      await this.updateProfile(profileId, {\n        status: 'ready',\n        current_action: 'Sẵn sàng chạy',\n        is_logged_in: true\n      });\n      this.notifyListeners('login_completed', profileId);\n      return {\n        success: true,\n        message: 'Login completed successfully'\n      };\n    } catch (error) {\n      console.error('Failed to complete login:', error);\n      throw error;\n    }\n  }\n  async startAutomation(profileId) {\n    try {\n      // Update profile status\n      await this.updateProfile(profileId, {\n        status: 'running',\n        current_action: 'Đang tương tác'\n      });\n      this.notifyListeners('automation_started', profileId);\n      return {\n        success: true,\n        message: 'Automation started'\n      };\n    } catch (error) {\n      console.error('Failed to start automation:', error);\n      throw error;\n    }\n  }\n  async pauseAutomation(profileId) {\n    try {\n      // Update profile status\n      await this.updateProfile(profileId, {\n        status: 'paused',\n        current_action: 'Đã tạm dừng'\n      });\n      this.notifyListeners('automation_paused', profileId);\n      return {\n        success: true,\n        message: 'Automation paused'\n      };\n    } catch (error) {\n      console.error('Failed to pause automation:', error);\n      throw error;\n    }\n  }\n  async stopAutomation(profileId) {\n    try {\n      // Update profile status\n      await this.updateProfile(profileId, {\n        status: 'ready',\n        current_action: 'Đã dừng'\n      });\n      this.notifyListeners('automation_stopped', profileId);\n      return {\n        success: true,\n        message: 'Automation stopped'\n      };\n    } catch (error) {\n      console.error('Failed to stop automation:', error);\n      throw error;\n    }\n  }\n\n  // Bulk operations\n  async bulkAction(profileIds, action) {\n    const results = [];\n    for (const profileId of profileIds) {\n      try {\n        let result;\n        switch (action) {\n          case 'start':\n            result = await this.startAutomation(profileId);\n            break;\n          case 'pause':\n            result = await this.pauseAutomation(profileId);\n            break;\n          case 'stop':\n            result = await this.stopAutomation(profileId);\n            break;\n          default:\n            throw new Error(`Unknown action: ${action}`);\n        }\n        results.push({\n          profileId,\n          success: true,\n          result\n        });\n      } catch (error) {\n        results.push({\n          profileId,\n          success: false,\n          error: error.message\n        });\n      }\n    }\n    this.notifyListeners('bulk_action_completed', {\n      action,\n      results\n    });\n    return results;\n  }\n\n  // Data transformation\n  transformProfileFromAPI(apiProfile) {\n    return {\n      id: apiProfile.id,\n      stt: apiProfile.id,\n      username: apiProfile.name,\n      proxy: apiProfile.proxy_info ? `${apiProfile.proxy_info.host}:${apiProfile.proxy_info.port}` : 'Local Network',\n      status: this.mapStatusToVietnamese(apiProfile.status || 'inactive'),\n      followersFollowed: 0,\n      // TODO: Get from automation data\n      followersToday: 0,\n      // TODO: Get from automation data\n      currentAction: apiProfile.current_action || 'Chờ đăng nhập',\n      actions: this.getAvailableActions(apiProfile),\n      isActive: apiProfile.is_active,\n      isLoggedIn: apiProfile.is_logged_in || false,\n      createdAt: apiProfile.created_at,\n      updatedAt: apiProfile.updated_at\n    };\n  }\n  mapStatusToVietnamese(status) {\n    const statusMap = {\n      'inactive': 'CHƯA ĐĂNG NHẬP',\n      'logging_in': 'ĐANG ĐĂNG NHẬP',\n      'ready': 'SẴN SÀNG',\n      'running': 'ĐANG NHẬP HOẠT ĐỘ',\n      'paused': 'TẠM DỪNG',\n      'error': 'LỖI',\n      'completed': 'ĐÃ HOÀN THÀNH'\n    };\n    return statusMap[status] || status.toUpperCase();\n  }\n  getAvailableActions(profile) {\n    const actions = [];\n    if (!profile.is_logged_in) {\n      actions.push('login');\n    } else if (profile.status === 'logging_in') {\n      actions.push('complete');\n    } else if (profile.status === 'ready') {\n      actions.push('start');\n    } else if (profile.status === 'running') {\n      actions.push('pause', 'stop');\n    } else if (profile.status === 'paused') {\n      actions.push('start', 'stop');\n    }\n    return actions;\n  }\n\n  // Getters\n  getProfiles() {\n    return this.profiles;\n  }\n  getProfile(profileId) {\n    return this.profiles.find(p => p.id === profileId);\n  }\n  getActiveProfiles() {\n    return this.profiles.filter(p => p.isActive);\n  }\n  getLoggedInProfiles() {\n    return this.profiles.filter(p => p.isLoggedIn);\n  }\n}\n\n// Create singleton instance\nconst profileService = new ProfileService();\nexport default profileService;", "map": {"version": 3, "names": ["profileAPI", "proxyAPI", "handleApiError", "ProfileService", "constructor", "profiles", "proxies", "listeners", "addListener", "callback", "push", "removeListener", "filter", "l", "notifyListeners", "event", "data", "for<PERSON>ach", "listener", "error", "console", "loadProfiles", "getProfiles", "map", "profile", "transformProfileFromAPI", "apiError", "Error", "message", "createProfile", "profileData", "proxyId", "proxyType", "proxy", "createProxyFromForm", "id", "apiProfileData", "name", "profileName", "description", "proxy_id", "auto_generate_fingerprint", "os_preference", "browser_preference", "newProfile", "transformedProfile", "updateProfile", "profileId", "updates", "updatedProfile", "index", "findIndex", "p", "deleteProfile", "testProfile", "result", "formData", "proxyData", "proxy_type", "host", "port", "parseInt", "username", "password", "validate_on_create", "createProxy", "validateProxy", "tempProxy", "deleteProxy", "startLogin", "status", "current_action", "success", "completeLogin", "is_logged_in", "startAutomation", "pauseAutomation", "stopAutomation", "bulkAction", "profileIds", "action", "results", "apiProfile", "stt", "proxy_info", "mapStatusToVietnamese", "followersFollowed", "followers<PERSON>oday", "currentAction", "actions", "getAvailableActions", "isActive", "is_active", "isLoggedIn", "createdAt", "created_at", "updatedAt", "updated_at", "statusMap", "toUpperCase", "getProfile", "find", "getActiveProfiles", "getLoggedInProfiles", "profileService"], "sources": ["/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/services/profileService.js"], "sourcesContent": ["/**\n * Profile Service - Manages browser profiles and proxy integration\n */\n\nimport { profileAPI, proxyAPI, handleApiError } from './api';\n\nclass ProfileService {\n  constructor() {\n    this.profiles = [];\n    this.proxies = [];\n    this.listeners = [];\n  }\n\n  // Event listener management\n  addListener(callback) {\n    this.listeners.push(callback);\n  }\n\n  removeListener(callback) {\n    this.listeners = this.listeners.filter(l => l !== callback);\n  }\n\n  notifyListeners(event, data) {\n    this.listeners.forEach(listener => {\n      try {\n        listener(event, data);\n      } catch (error) {\n        console.error('Error in profile service listener:', error);\n      }\n    });\n  }\n\n  // Profile management\n  async loadProfiles() {\n    try {\n      const profiles = await profileAPI.getProfiles();\n      this.profiles = profiles.map(profile => this.transformProfileFromAPI(profile));\n      this.notifyListeners('profiles_loaded', this.profiles);\n      return this.profiles;\n    } catch (error) {\n      const apiError = handleApiError(error);\n      console.error('Failed to load profiles:', apiError);\n      throw new Error(apiError.message);\n    }\n  }\n\n  async createProfile(profileData) {\n    try {\n      // First create proxy if needed\n      let proxyId = null;\n      if (profileData.proxyType !== 'no-proxy') {\n        const proxy = await this.createProxyFromForm(profileData);\n        proxyId = proxy.id;\n      }\n\n      // Create profile\n      const apiProfileData = {\n        name: profileData.profileName,\n        description: `Profile with ${profileData.proxyType} proxy`,\n        proxy_id: proxyId,\n        auto_generate_fingerprint: true,\n        os_preference: 'windows',\n        browser_preference: 'firefox'\n      };\n\n      const newProfile = await profileAPI.createProfile(apiProfileData);\n      const transformedProfile = this.transformProfileFromAPI(newProfile);\n      \n      this.profiles.push(transformedProfile);\n      this.notifyListeners('profile_created', transformedProfile);\n      \n      return transformedProfile;\n    } catch (error) {\n      const apiError = handleApiError(error);\n      console.error('Failed to create profile:', apiError);\n      throw new Error(apiError.message);\n    }\n  }\n\n  async updateProfile(profileId, updates) {\n    try {\n      const updatedProfile = await profileAPI.updateProfile(profileId, updates);\n      const transformedProfile = this.transformProfileFromAPI(updatedProfile);\n      \n      const index = this.profiles.findIndex(p => p.id === profileId);\n      if (index !== -1) {\n        this.profiles[index] = transformedProfile;\n        this.notifyListeners('profile_updated', transformedProfile);\n      }\n      \n      return transformedProfile;\n    } catch (error) {\n      const apiError = handleApiError(error);\n      console.error('Failed to update profile:', apiError);\n      throw new Error(apiError.message);\n    }\n  }\n\n  async deleteProfile(profileId) {\n    try {\n      await profileAPI.deleteProfile(profileId);\n      this.profiles = this.profiles.filter(p => p.id !== profileId);\n      this.notifyListeners('profile_deleted', profileId);\n      return true;\n    } catch (error) {\n      const apiError = handleApiError(error);\n      console.error('Failed to delete profile:', apiError);\n      throw new Error(apiError.message);\n    }\n  }\n\n  async testProfile(profileId) {\n    try {\n      const result = await profileAPI.testProfile(profileId);\n      this.notifyListeners('profile_tested', { profileId, result });\n      return result;\n    } catch (error) {\n      const apiError = handleApiError(error);\n      console.error('Failed to test profile:', apiError);\n      throw new Error(apiError.message);\n    }\n  }\n\n  // Proxy management\n  async createProxyFromForm(formData) {\n    try {\n      const proxyData = {\n        name: `${formData.profileName}_proxy`,\n        proxy_type: formData.proxyType,\n        host: formData.host,\n        port: parseInt(formData.port),\n        username: formData.username || null,\n        password: formData.password || null,\n        description: `Proxy for profile ${formData.profileName}`,\n        validate_on_create: true\n      };\n\n      const proxy = await proxyAPI.createProxy(proxyData);\n      return proxy;\n    } catch (error) {\n      const apiError = handleApiError(error);\n      console.error('Failed to create proxy:', apiError);\n      throw new Error(apiError.message);\n    }\n  }\n\n  async validateProxy(proxyData) {\n    try {\n      // Create temporary proxy for validation\n      const tempProxy = await this.createProxyFromForm({\n        ...proxyData,\n        profileName: 'temp_validation'\n      });\n\n      // Validate the proxy\n      const result = await proxyAPI.validateProxy(tempProxy.id);\n      \n      // Clean up temporary proxy\n      await proxyAPI.deleteProxy(tempProxy.id);\n      \n      return result;\n    } catch (error) {\n      const apiError = handleApiError(error);\n      console.error('Failed to validate proxy:', apiError);\n      throw new Error(apiError.message);\n    }\n  }\n\n  // Profile actions\n  async startLogin(profileId) {\n    try {\n      // Update profile status\n      await this.updateProfile(profileId, { \n        status: 'logging_in',\n        current_action: 'Đang đăng nhập...'\n      });\n\n      // TODO: Implement actual browser launch for login\n      this.notifyListeners('login_started', profileId);\n      \n      return { success: true, message: 'Login process started' };\n    } catch (error) {\n      console.error('Failed to start login:', error);\n      throw error;\n    }\n  }\n\n  async completeLogin(profileId) {\n    try {\n      // Update profile status\n      await this.updateProfile(profileId, { \n        status: 'ready',\n        current_action: 'Sẵn sàng chạy',\n        is_logged_in: true\n      });\n\n      this.notifyListeners('login_completed', profileId);\n      \n      return { success: true, message: 'Login completed successfully' };\n    } catch (error) {\n      console.error('Failed to complete login:', error);\n      throw error;\n    }\n  }\n\n  async startAutomation(profileId) {\n    try {\n      // Update profile status\n      await this.updateProfile(profileId, { \n        status: 'running',\n        current_action: 'Đang tương tác'\n      });\n\n      this.notifyListeners('automation_started', profileId);\n      \n      return { success: true, message: 'Automation started' };\n    } catch (error) {\n      console.error('Failed to start automation:', error);\n      throw error;\n    }\n  }\n\n  async pauseAutomation(profileId) {\n    try {\n      // Update profile status\n      await this.updateProfile(profileId, { \n        status: 'paused',\n        current_action: 'Đã tạm dừng'\n      });\n\n      this.notifyListeners('automation_paused', profileId);\n      \n      return { success: true, message: 'Automation paused' };\n    } catch (error) {\n      console.error('Failed to pause automation:', error);\n      throw error;\n    }\n  }\n\n  async stopAutomation(profileId) {\n    try {\n      // Update profile status\n      await this.updateProfile(profileId, { \n        status: 'ready',\n        current_action: 'Đã dừng'\n      });\n\n      this.notifyListeners('automation_stopped', profileId);\n      \n      return { success: true, message: 'Automation stopped' };\n    } catch (error) {\n      console.error('Failed to stop automation:', error);\n      throw error;\n    }\n  }\n\n  // Bulk operations\n  async bulkAction(profileIds, action) {\n    const results = [];\n    \n    for (const profileId of profileIds) {\n      try {\n        let result;\n        switch (action) {\n          case 'start':\n            result = await this.startAutomation(profileId);\n            break;\n          case 'pause':\n            result = await this.pauseAutomation(profileId);\n            break;\n          case 'stop':\n            result = await this.stopAutomation(profileId);\n            break;\n          default:\n            throw new Error(`Unknown action: ${action}`);\n        }\n        results.push({ profileId, success: true, result });\n      } catch (error) {\n        results.push({ profileId, success: false, error: error.message });\n      }\n    }\n\n    this.notifyListeners('bulk_action_completed', { action, results });\n    return results;\n  }\n\n  // Data transformation\n  transformProfileFromAPI(apiProfile) {\n    return {\n      id: apiProfile.id,\n      stt: apiProfile.id,\n      username: apiProfile.name,\n      proxy: apiProfile.proxy_info ? \n        `${apiProfile.proxy_info.host}:${apiProfile.proxy_info.port}` : \n        'Local Network',\n      status: this.mapStatusToVietnamese(apiProfile.status || 'inactive'),\n      followersFollowed: 0, // TODO: Get from automation data\n      followersToday: 0, // TODO: Get from automation data\n      currentAction: apiProfile.current_action || 'Chờ đăng nhập',\n      actions: this.getAvailableActions(apiProfile),\n      isActive: apiProfile.is_active,\n      isLoggedIn: apiProfile.is_logged_in || false,\n      createdAt: apiProfile.created_at,\n      updatedAt: apiProfile.updated_at\n    };\n  }\n\n  mapStatusToVietnamese(status) {\n    const statusMap = {\n      'inactive': 'CHƯA ĐĂNG NHẬP',\n      'logging_in': 'ĐANG ĐĂNG NHẬP',\n      'ready': 'SẴN SÀNG',\n      'running': 'ĐANG NHẬP HOẠT ĐỘ',\n      'paused': 'TẠM DỪNG',\n      'error': 'LỖI',\n      'completed': 'ĐÃ HOÀN THÀNH'\n    };\n    return statusMap[status] || status.toUpperCase();\n  }\n\n  getAvailableActions(profile) {\n    const actions = [];\n    \n    if (!profile.is_logged_in) {\n      actions.push('login');\n    } else if (profile.status === 'logging_in') {\n      actions.push('complete');\n    } else if (profile.status === 'ready') {\n      actions.push('start');\n    } else if (profile.status === 'running') {\n      actions.push('pause', 'stop');\n    } else if (profile.status === 'paused') {\n      actions.push('start', 'stop');\n    }\n    \n    return actions;\n  }\n\n  // Getters\n  getProfiles() {\n    return this.profiles;\n  }\n\n  getProfile(profileId) {\n    return this.profiles.find(p => p.id === profileId);\n  }\n\n  getActiveProfiles() {\n    return this.profiles.filter(p => p.isActive);\n  }\n\n  getLoggedInProfiles() {\n    return this.profiles.filter(p => p.isLoggedIn);\n  }\n}\n\n// Create singleton instance\nconst profileService = new ProfileService();\n\nexport default profileService;\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SAASA,UAAU,EAAEC,QAAQ,EAAEC,cAAc,QAAQ,OAAO;AAE5D,MAAMC,cAAc,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,SAAS,GAAG,EAAE;EACrB;;EAEA;EACAC,WAAWA,CAACC,QAAQ,EAAE;IACpB,IAAI,CAACF,SAAS,CAACG,IAAI,CAACD,QAAQ,CAAC;EAC/B;EAEAE,cAAcA,CAACF,QAAQ,EAAE;IACvB,IAAI,CAACF,SAAS,GAAG,IAAI,CAACA,SAAS,CAACK,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKJ,QAAQ,CAAC;EAC7D;EAEAK,eAAeA,CAACC,KAAK,EAAEC,IAAI,EAAE;IAC3B,IAAI,CAACT,SAAS,CAACU,OAAO,CAACC,QAAQ,IAAI;MACjC,IAAI;QACFA,QAAQ,CAACH,KAAK,EAAEC,IAAI,CAAC;MACvB,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC5D;IACF,CAAC,CAAC;EACJ;;EAEA;EACA,MAAME,YAAYA,CAAA,EAAG;IACnB,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAML,UAAU,CAACsB,WAAW,CAAC,CAAC;MAC/C,IAAI,CAACjB,QAAQ,GAAGA,QAAQ,CAACkB,GAAG,CAACC,OAAO,IAAI,IAAI,CAACC,uBAAuB,CAACD,OAAO,CAAC,CAAC;MAC9E,IAAI,CAACV,eAAe,CAAC,iBAAiB,EAAE,IAAI,CAACT,QAAQ,CAAC;MACtD,OAAO,IAAI,CAACA,QAAQ;IACtB,CAAC,CAAC,OAAOc,KAAK,EAAE;MACd,MAAMO,QAAQ,GAAGxB,cAAc,CAACiB,KAAK,CAAC;MACtCC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEO,QAAQ,CAAC;MACnD,MAAM,IAAIC,KAAK,CAACD,QAAQ,CAACE,OAAO,CAAC;IACnC;EACF;EAEA,MAAMC,aAAaA,CAACC,WAAW,EAAE;IAC/B,IAAI;MACF;MACA,IAAIC,OAAO,GAAG,IAAI;MAClB,IAAID,WAAW,CAACE,SAAS,KAAK,UAAU,EAAE;QACxC,MAAMC,KAAK,GAAG,MAAM,IAAI,CAACC,mBAAmB,CAACJ,WAAW,CAAC;QACzDC,OAAO,GAAGE,KAAK,CAACE,EAAE;MACpB;;MAEA;MACA,MAAMC,cAAc,GAAG;QACrBC,IAAI,EAAEP,WAAW,CAACQ,WAAW;QAC7BC,WAAW,EAAE,gBAAgBT,WAAW,CAACE,SAAS,QAAQ;QAC1DQ,QAAQ,EAAET,OAAO;QACjBU,yBAAyB,EAAE,IAAI;QAC/BC,aAAa,EAAE,SAAS;QACxBC,kBAAkB,EAAE;MACtB,CAAC;MAED,MAAMC,UAAU,GAAG,MAAM5C,UAAU,CAAC6B,aAAa,CAACO,cAAc,CAAC;MACjE,MAAMS,kBAAkB,GAAG,IAAI,CAACpB,uBAAuB,CAACmB,UAAU,CAAC;MAEnE,IAAI,CAACvC,QAAQ,CAACK,IAAI,CAACmC,kBAAkB,CAAC;MACtC,IAAI,CAAC/B,eAAe,CAAC,iBAAiB,EAAE+B,kBAAkB,CAAC;MAE3D,OAAOA,kBAAkB;IAC3B,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACd,MAAMO,QAAQ,GAAGxB,cAAc,CAACiB,KAAK,CAAC;MACtCC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEO,QAAQ,CAAC;MACpD,MAAM,IAAIC,KAAK,CAACD,QAAQ,CAACE,OAAO,CAAC;IACnC;EACF;EAEA,MAAMkB,aAAaA,CAACC,SAAS,EAAEC,OAAO,EAAE;IACtC,IAAI;MACF,MAAMC,cAAc,GAAG,MAAMjD,UAAU,CAAC8C,aAAa,CAACC,SAAS,EAAEC,OAAO,CAAC;MACzE,MAAMH,kBAAkB,GAAG,IAAI,CAACpB,uBAAuB,CAACwB,cAAc,CAAC;MAEvE,MAAMC,KAAK,GAAG,IAAI,CAAC7C,QAAQ,CAAC8C,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACjB,EAAE,KAAKY,SAAS,CAAC;MAC9D,IAAIG,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,IAAI,CAAC7C,QAAQ,CAAC6C,KAAK,CAAC,GAAGL,kBAAkB;QACzC,IAAI,CAAC/B,eAAe,CAAC,iBAAiB,EAAE+B,kBAAkB,CAAC;MAC7D;MAEA,OAAOA,kBAAkB;IAC3B,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACd,MAAMO,QAAQ,GAAGxB,cAAc,CAACiB,KAAK,CAAC;MACtCC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEO,QAAQ,CAAC;MACpD,MAAM,IAAIC,KAAK,CAACD,QAAQ,CAACE,OAAO,CAAC;IACnC;EACF;EAEA,MAAMyB,aAAaA,CAACN,SAAS,EAAE;IAC7B,IAAI;MACF,MAAM/C,UAAU,CAACqD,aAAa,CAACN,SAAS,CAAC;MACzC,IAAI,CAAC1C,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACO,MAAM,CAACwC,CAAC,IAAIA,CAAC,CAACjB,EAAE,KAAKY,SAAS,CAAC;MAC7D,IAAI,CAACjC,eAAe,CAAC,iBAAiB,EAAEiC,SAAS,CAAC;MAClD,OAAO,IAAI;IACb,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACd,MAAMO,QAAQ,GAAGxB,cAAc,CAACiB,KAAK,CAAC;MACtCC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEO,QAAQ,CAAC;MACpD,MAAM,IAAIC,KAAK,CAACD,QAAQ,CAACE,OAAO,CAAC;IACnC;EACF;EAEA,MAAM0B,WAAWA,CAACP,SAAS,EAAE;IAC3B,IAAI;MACF,MAAMQ,MAAM,GAAG,MAAMvD,UAAU,CAACsD,WAAW,CAACP,SAAS,CAAC;MACtD,IAAI,CAACjC,eAAe,CAAC,gBAAgB,EAAE;QAAEiC,SAAS;QAAEQ;MAAO,CAAC,CAAC;MAC7D,OAAOA,MAAM;IACf,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACd,MAAMO,QAAQ,GAAGxB,cAAc,CAACiB,KAAK,CAAC;MACtCC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEO,QAAQ,CAAC;MAClD,MAAM,IAAIC,KAAK,CAACD,QAAQ,CAACE,OAAO,CAAC;IACnC;EACF;;EAEA;EACA,MAAMM,mBAAmBA,CAACsB,QAAQ,EAAE;IAClC,IAAI;MACF,MAAMC,SAAS,GAAG;QAChBpB,IAAI,EAAE,GAAGmB,QAAQ,CAAClB,WAAW,QAAQ;QACrCoB,UAAU,EAAEF,QAAQ,CAACxB,SAAS;QAC9B2B,IAAI,EAAEH,QAAQ,CAACG,IAAI;QACnBC,IAAI,EAAEC,QAAQ,CAACL,QAAQ,CAACI,IAAI,CAAC;QAC7BE,QAAQ,EAAEN,QAAQ,CAACM,QAAQ,IAAI,IAAI;QACnCC,QAAQ,EAAEP,QAAQ,CAACO,QAAQ,IAAI,IAAI;QACnCxB,WAAW,EAAE,qBAAqBiB,QAAQ,CAAClB,WAAW,EAAE;QACxD0B,kBAAkB,EAAE;MACtB,CAAC;MAED,MAAM/B,KAAK,GAAG,MAAMhC,QAAQ,CAACgE,WAAW,CAACR,SAAS,CAAC;MACnD,OAAOxB,KAAK;IACd,CAAC,CAAC,OAAOd,KAAK,EAAE;MACd,MAAMO,QAAQ,GAAGxB,cAAc,CAACiB,KAAK,CAAC;MACtCC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEO,QAAQ,CAAC;MAClD,MAAM,IAAIC,KAAK,CAACD,QAAQ,CAACE,OAAO,CAAC;IACnC;EACF;EAEA,MAAMsC,aAAaA,CAACT,SAAS,EAAE;IAC7B,IAAI;MACF;MACA,MAAMU,SAAS,GAAG,MAAM,IAAI,CAACjC,mBAAmB,CAAC;QAC/C,GAAGuB,SAAS;QACZnB,WAAW,EAAE;MACf,CAAC,CAAC;;MAEF;MACA,MAAMiB,MAAM,GAAG,MAAMtD,QAAQ,CAACiE,aAAa,CAACC,SAAS,CAAChC,EAAE,CAAC;;MAEzD;MACA,MAAMlC,QAAQ,CAACmE,WAAW,CAACD,SAAS,CAAChC,EAAE,CAAC;MAExC,OAAOoB,MAAM;IACf,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACd,MAAMO,QAAQ,GAAGxB,cAAc,CAACiB,KAAK,CAAC;MACtCC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEO,QAAQ,CAAC;MACpD,MAAM,IAAIC,KAAK,CAACD,QAAQ,CAACE,OAAO,CAAC;IACnC;EACF;;EAEA;EACA,MAAMyC,UAAUA,CAACtB,SAAS,EAAE;IAC1B,IAAI;MACF;MACA,MAAM,IAAI,CAACD,aAAa,CAACC,SAAS,EAAE;QAClCuB,MAAM,EAAE,YAAY;QACpBC,cAAc,EAAE;MAClB,CAAC,CAAC;;MAEF;MACA,IAAI,CAACzD,eAAe,CAAC,eAAe,EAAEiC,SAAS,CAAC;MAEhD,OAAO;QAAEyB,OAAO,EAAE,IAAI;QAAE5C,OAAO,EAAE;MAAwB,CAAC;IAC5D,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb;EACF;EAEA,MAAMsD,aAAaA,CAAC1B,SAAS,EAAE;IAC7B,IAAI;MACF;MACA,MAAM,IAAI,CAACD,aAAa,CAACC,SAAS,EAAE;QAClCuB,MAAM,EAAE,OAAO;QACfC,cAAc,EAAE,eAAe;QAC/BG,YAAY,EAAE;MAChB,CAAC,CAAC;MAEF,IAAI,CAAC5D,eAAe,CAAC,iBAAiB,EAAEiC,SAAS,CAAC;MAElD,OAAO;QAAEyB,OAAO,EAAE,IAAI;QAAE5C,OAAO,EAAE;MAA+B,CAAC;IACnE,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF;EAEA,MAAMwD,eAAeA,CAAC5B,SAAS,EAAE;IAC/B,IAAI;MACF;MACA,MAAM,IAAI,CAACD,aAAa,CAACC,SAAS,EAAE;QAClCuB,MAAM,EAAE,SAAS;QACjBC,cAAc,EAAE;MAClB,CAAC,CAAC;MAEF,IAAI,CAACzD,eAAe,CAAC,oBAAoB,EAAEiC,SAAS,CAAC;MAErD,OAAO;QAAEyB,OAAO,EAAE,IAAI;QAAE5C,OAAO,EAAE;MAAqB,CAAC;IACzD,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK;IACb;EACF;EAEA,MAAMyD,eAAeA,CAAC7B,SAAS,EAAE;IAC/B,IAAI;MACF;MACA,MAAM,IAAI,CAACD,aAAa,CAACC,SAAS,EAAE;QAClCuB,MAAM,EAAE,QAAQ;QAChBC,cAAc,EAAE;MAClB,CAAC,CAAC;MAEF,IAAI,CAACzD,eAAe,CAAC,mBAAmB,EAAEiC,SAAS,CAAC;MAEpD,OAAO;QAAEyB,OAAO,EAAE,IAAI;QAAE5C,OAAO,EAAE;MAAoB,CAAC;IACxD,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK;IACb;EACF;EAEA,MAAM0D,cAAcA,CAAC9B,SAAS,EAAE;IAC9B,IAAI;MACF;MACA,MAAM,IAAI,CAACD,aAAa,CAACC,SAAS,EAAE;QAClCuB,MAAM,EAAE,OAAO;QACfC,cAAc,EAAE;MAClB,CAAC,CAAC;MAEF,IAAI,CAACzD,eAAe,CAAC,oBAAoB,EAAEiC,SAAS,CAAC;MAErD,OAAO;QAAEyB,OAAO,EAAE,IAAI;QAAE5C,OAAO,EAAE;MAAqB,CAAC;IACzD,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAM2D,UAAUA,CAACC,UAAU,EAAEC,MAAM,EAAE;IACnC,MAAMC,OAAO,GAAG,EAAE;IAElB,KAAK,MAAMlC,SAAS,IAAIgC,UAAU,EAAE;MAClC,IAAI;QACF,IAAIxB,MAAM;QACV,QAAQyB,MAAM;UACZ,KAAK,OAAO;YACVzB,MAAM,GAAG,MAAM,IAAI,CAACoB,eAAe,CAAC5B,SAAS,CAAC;YAC9C;UACF,KAAK,OAAO;YACVQ,MAAM,GAAG,MAAM,IAAI,CAACqB,eAAe,CAAC7B,SAAS,CAAC;YAC9C;UACF,KAAK,MAAM;YACTQ,MAAM,GAAG,MAAM,IAAI,CAACsB,cAAc,CAAC9B,SAAS,CAAC;YAC7C;UACF;YACE,MAAM,IAAIpB,KAAK,CAAC,mBAAmBqD,MAAM,EAAE,CAAC;QAChD;QACAC,OAAO,CAACvE,IAAI,CAAC;UAAEqC,SAAS;UAAEyB,OAAO,EAAE,IAAI;UAAEjB;QAAO,CAAC,CAAC;MACpD,CAAC,CAAC,OAAOpC,KAAK,EAAE;QACd8D,OAAO,CAACvE,IAAI,CAAC;UAAEqC,SAAS;UAAEyB,OAAO,EAAE,KAAK;UAAErD,KAAK,EAAEA,KAAK,CAACS;QAAQ,CAAC,CAAC;MACnE;IACF;IAEA,IAAI,CAACd,eAAe,CAAC,uBAAuB,EAAE;MAAEkE,MAAM;MAAEC;IAAQ,CAAC,CAAC;IAClE,OAAOA,OAAO;EAChB;;EAEA;EACAxD,uBAAuBA,CAACyD,UAAU,EAAE;IAClC,OAAO;MACL/C,EAAE,EAAE+C,UAAU,CAAC/C,EAAE;MACjBgD,GAAG,EAAED,UAAU,CAAC/C,EAAE;MAClB2B,QAAQ,EAAEoB,UAAU,CAAC7C,IAAI;MACzBJ,KAAK,EAAEiD,UAAU,CAACE,UAAU,GAC1B,GAAGF,UAAU,CAACE,UAAU,CAACzB,IAAI,IAAIuB,UAAU,CAACE,UAAU,CAACxB,IAAI,EAAE,GAC7D,eAAe;MACjBU,MAAM,EAAE,IAAI,CAACe,qBAAqB,CAACH,UAAU,CAACZ,MAAM,IAAI,UAAU,CAAC;MACnEgB,iBAAiB,EAAE,CAAC;MAAE;MACtBC,cAAc,EAAE,CAAC;MAAE;MACnBC,aAAa,EAAEN,UAAU,CAACX,cAAc,IAAI,eAAe;MAC3DkB,OAAO,EAAE,IAAI,CAACC,mBAAmB,CAACR,UAAU,CAAC;MAC7CS,QAAQ,EAAET,UAAU,CAACU,SAAS;MAC9BC,UAAU,EAAEX,UAAU,CAACR,YAAY,IAAI,KAAK;MAC5CoB,SAAS,EAAEZ,UAAU,CAACa,UAAU;MAChCC,SAAS,EAAEd,UAAU,CAACe;IACxB,CAAC;EACH;EAEAZ,qBAAqBA,CAACf,MAAM,EAAE;IAC5B,MAAM4B,SAAS,GAAG;MAChB,UAAU,EAAE,gBAAgB;MAC5B,YAAY,EAAE,gBAAgB;MAC9B,OAAO,EAAE,UAAU;MACnB,SAAS,EAAE,mBAAmB;MAC9B,QAAQ,EAAE,UAAU;MACpB,OAAO,EAAE,KAAK;MACd,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,SAAS,CAAC5B,MAAM,CAAC,IAAIA,MAAM,CAAC6B,WAAW,CAAC,CAAC;EAClD;EAEAT,mBAAmBA,CAAClE,OAAO,EAAE;IAC3B,MAAMiE,OAAO,GAAG,EAAE;IAElB,IAAI,CAACjE,OAAO,CAACkD,YAAY,EAAE;MACzBe,OAAO,CAAC/E,IAAI,CAAC,OAAO,CAAC;IACvB,CAAC,MAAM,IAAIc,OAAO,CAAC8C,MAAM,KAAK,YAAY,EAAE;MAC1CmB,OAAO,CAAC/E,IAAI,CAAC,UAAU,CAAC;IAC1B,CAAC,MAAM,IAAIc,OAAO,CAAC8C,MAAM,KAAK,OAAO,EAAE;MACrCmB,OAAO,CAAC/E,IAAI,CAAC,OAAO,CAAC;IACvB,CAAC,MAAM,IAAIc,OAAO,CAAC8C,MAAM,KAAK,SAAS,EAAE;MACvCmB,OAAO,CAAC/E,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC;IAC/B,CAAC,MAAM,IAAIc,OAAO,CAAC8C,MAAM,KAAK,QAAQ,EAAE;MACtCmB,OAAO,CAAC/E,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC;IAC/B;IAEA,OAAO+E,OAAO;EAChB;;EAEA;EACAnE,WAAWA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACjB,QAAQ;EACtB;EAEA+F,UAAUA,CAACrD,SAAS,EAAE;IACpB,OAAO,IAAI,CAAC1C,QAAQ,CAACgG,IAAI,CAACjD,CAAC,IAAIA,CAAC,CAACjB,EAAE,KAAKY,SAAS,CAAC;EACpD;EAEAuD,iBAAiBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACjG,QAAQ,CAACO,MAAM,CAACwC,CAAC,IAAIA,CAAC,CAACuC,QAAQ,CAAC;EAC9C;EAEAY,mBAAmBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAClG,QAAQ,CAACO,MAAM,CAACwC,CAAC,IAAIA,CAAC,CAACyC,UAAU,CAAC;EAChD;AACF;;AAEA;AACA,MAAMW,cAAc,GAAG,IAAIrG,cAAc,CAAC,CAAC;AAE3C,eAAeqG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}