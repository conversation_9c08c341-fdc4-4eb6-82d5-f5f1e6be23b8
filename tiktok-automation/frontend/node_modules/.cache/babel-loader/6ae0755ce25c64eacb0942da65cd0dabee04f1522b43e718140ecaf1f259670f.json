{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Dashboard/AntidetectTester.jsx\",\n  _s = $RefreshSig$();\n/**\n * Antidetect Testing Component\n */\n\nimport React, { useState } from 'react';\nimport { FiPlay, FiCheck, FiX, FiLoader, FiShield, FiAlertTriangle } from 'react-icons/fi';\nimport { antidetectAPI, handleApiError } from '../../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AntidetectTester = ({\n  profileId,\n  onClose\n}) => {\n  _s();\n  const [isRunning, setIsRunning] = useState(false);\n  const [testResults, setTestResults] = useState(null);\n  const [selectedTests, setSelectedTests] = useState({\n    bot_sannysoft: true,\n    intoli_headless: true,\n    areyouheadless: true,\n    pixelscan: true,\n    browserleaks_webrtc: true,\n    tiktok_access: true\n  });\n  const [testOptions, setTestOptions] = useState({\n    headless: false,\n    includeTikTok: true\n  });\n  const testSites = {\n    bot_sannysoft: {\n      name: 'Sannysoft Bot Detector',\n      description: 'Comprehensive bot detection tests'\n    },\n    intoli_headless: {\n      name: 'Intoli Headless Test',\n      description: 'Headless browser detection'\n    },\n    areyouheadless: {\n      name: 'Are You Headless',\n      description: 'Advanced headless detection'\n    },\n    pixelscan: {\n      name: 'PixelScan Fingerprint',\n      description: 'Canvas and WebGL fingerprinting'\n    },\n    browserleaks_webrtc: {\n      name: 'BrowserLeaks WebRTC',\n      description: 'WebRTC leak detection'\n    },\n    tiktok_access: {\n      name: 'TikTok Access Test',\n      description: 'Direct TikTok bot detection test'\n    }\n  };\n  const runAntidetectTest = async () => {\n    setIsRunning(true);\n    setTestResults(null);\n    try {\n      const selectedSiteKeys = Object.keys(selectedTests).filter(key => selectedTests[key] && key !== 'tiktok_access');\n      const result = await antidetectAPI.testProfile(profileId, selectedSiteKeys, selectedTests.tiktok_access, testOptions.headless);\n      setTestResults(result);\n    } catch (error) {\n      console.error('Antidetect test failed:', error);\n      const apiError = handleApiError(error);\n      setTestResults({\n        success: false,\n        error: apiError.message,\n        overall_score: 0,\n        test_results: {},\n        recommendations: ['Failed to run test: ' + apiError.message]\n      });\n    } finally {\n      setIsRunning(false);\n    }\n  };\n  const getScoreColor = score => {\n    if (score >= 80) return 'text-green-600';\n    if (score >= 60) return 'text-yellow-600';\n    return 'text-red-600';\n  };\n  const getScoreIcon = score => {\n    if (score >= 80) return /*#__PURE__*/_jsxDEV(FiCheck, {\n      className: \"text-green-600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 29\n    }, this);\n    if (score >= 60) return /*#__PURE__*/_jsxDEV(FiAlertTriangle, {\n      className: \"text-yellow-600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 29\n    }, this);\n    return /*#__PURE__*/_jsxDEV(FiX, {\n      className: \"text-red-600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 12\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between p-6 border-b\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(FiShield, {\n            className: \"text-blue-600 text-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold\",\n            children: \"Antidetect Testing\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"text-gray-400 hover:text-gray-600\",\n          children: /*#__PURE__*/_jsxDEV(FiX, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium mb-4\",\n            children: \"Test Configuration\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n            children: Object.entries(testSites).map(([key, site]) => /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"flex items-start space-x-3 p-3 border rounded-lg hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: selectedTests[key],\n                onChange: e => setSelectedTests(prev => ({\n                  ...prev,\n                  [key]: e.target.checked\n                })),\n                className: \"mt-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-medium\",\n                  children: site.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 114,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600\",\n                  children: site.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 19\n              }, this)]\n            }, key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-6\",\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: testOptions.headless,\n                onChange: e => setTestOptions(prev => ({\n                  ...prev,\n                  headless: e.target.checked\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Run in headless mode\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: runAntidetectTest,\n            disabled: isRunning || Object.values(selectedTests).every(v => !v),\n            className: \"flex items-center space-x-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n            children: isRunning ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(FiLoader, {\n                className: \"animate-spin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Running Tests...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(FiPlay, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Run Antidetect Test\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), testResults && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-50 p-4 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium\",\n                children: \"Overall Detection Score\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [getScoreIcon(testResults.overall_score), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `text-2xl font-bold ${getScoreColor(testResults.overall_score)}`,\n                  children: [testResults.overall_score, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-gray-200 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `h-2 rounded-full ${testResults.overall_score >= 80 ? 'bg-green-600' : testResults.overall_score >= 60 ? 'bg-yellow-600' : 'bg-red-600'}`,\n                  style: {\n                    width: `${testResults.overall_score}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this), testResults.success && testResults.test_results && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium mb-4\",\n              children: \"Test Results\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: Object.entries(testResults.test_results).map(([testKey, result]) => {\n                var _testSites$testKey;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"border rounded-lg p-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-medium\",\n                      children: ((_testSites$testKey = testSites[testKey]) === null || _testSites$testKey === void 0 ? void 0 : _testSites$testKey.name) || testKey\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 193,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2\",\n                      children: [result.success ? getScoreIcon(result.score || 0) : /*#__PURE__*/_jsxDEV(FiX, {\n                        className: \"text-red-600\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 197,\n                        columnNumber: 81\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `font-bold ${getScoreColor(result.score || 0)}`,\n                        children: result.score ? `${result.score}%` : 'Failed'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 198,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 196,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 25\n                  }, this), result.success && result.check_results && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-600\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [\"Checks passed: \", result.checks_passed, \"/\", result.total_checks]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 206,\n                      columnNumber: 29\n                    }, this), result.detection_indicators && result.detection_indicators.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-red-600 mt-1\",\n                      children: [\"Issues: \", result.detection_indicators.join(', ')]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 208,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 27\n                  }, this), result.error && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-red-600 text-sm\",\n                    children: [\"Error: \", result.error]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 27\n                  }, this)]\n                }, testKey, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 23\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 17\n          }, this), testResults.recommendations && testResults.recommendations.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium mb-4\",\n              children: \"Recommendations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n              children: /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"space-y-2\",\n                children: testResults.recommendations.map((recommendation, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"flex items-start space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-blue-600 mt-1\",\n                    children: \"\\u2022\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-blue-800\",\n                    children: recommendation\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 27\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 17\n          }, this), !testResults.success && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 text-red-800\",\n              children: [/*#__PURE__*/_jsxDEV(FiX, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: \"Test Failed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-red-700 mt-2\",\n              children: testResults.error || 'Unknown error occurred'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 5\n  }, this);\n};\n_s(AntidetectTester, \"6Wid7QIfH1u4JEo0mm2jRmP0BjM=\");\n_c = AntidetectTester;\nexport default AntidetectTester;\nvar _c;\n$RefreshReg$(_c, \"AntidetectTester\");", "map": {"version": 3, "names": ["React", "useState", "FiPlay", "<PERSON><PERSON><PERSON><PERSON>", "FiX", "<PERSON><PERSON><PERSON><PERSON>", "FiShield", "FiAlertTriangle", "antidetectAPI", "handleApiError", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AntidetectTester", "profileId", "onClose", "_s", "isRunning", "setIsRunning", "testResults", "setTestResults", "selectedTests", "setSelectedTests", "bot_sannysoft", "intoli_headless", "areyouheadless", "pixelscan", "browserleaks_webrtc", "tiktok_access", "testOptions", "setTestOptions", "headless", "includeTikTok", "testSites", "name", "description", "runAntidetectTest", "selected<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "filter", "key", "result", "testProfile", "error", "console", "apiError", "success", "message", "overall_score", "test_results", "recommendations", "getScoreColor", "score", "getScoreIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "onClick", "size", "entries", "map", "site", "type", "checked", "onChange", "e", "prev", "target", "disabled", "values", "every", "v", "style", "width", "<PERSON><PERSON><PERSON>", "_testSites$testKey", "check_results", "checks_passed", "total_checks", "detection_indicators", "length", "join", "recommendation", "index", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Dashboard/AntidetectTester.jsx"], "sourcesContent": ["/**\n * Antidetect Testing Component\n */\n\nimport React, { useState } from 'react';\nimport { <PERSON><PERSON>lay, FiCheck, FiX, <PERSON>Loader, FiShield, FiAlertTriangle } from 'react-icons/fi';\nimport { antidetectAPI, handleApiError } from '../../services/api';\n\nconst AntidetectTester = ({ profileId, onClose }) => {\n  const [isRunning, setIsRunning] = useState(false);\n  const [testResults, setTestResults] = useState(null);\n  const [selectedTests, setSelectedTests] = useState({\n    bot_sannysoft: true,\n    intoli_headless: true,\n    areyouheadless: true,\n    pixelscan: true,\n    browserleaks_webrtc: true,\n    tiktok_access: true\n  });\n  const [testOptions, setTestOptions] = useState({\n    headless: false,\n    includeTikTok: true\n  });\n\n  const testSites = {\n    bot_sannysoft: { name: 'Sannysoft Bot Detector', description: 'Comprehensive bot detection tests' },\n    intoli_headless: { name: 'Intoli Headless Test', description: 'Headless browser detection' },\n    areyouheadless: { name: 'Are You Headless', description: 'Advanced headless detection' },\n    pixelscan: { name: 'PixelScan Fingerprint', description: 'Canvas and WebGL fingerprinting' },\n    browserleaks_webrtc: { name: 'BrowserLeaks WebRTC', description: 'WebRTC leak detection' },\n    tiktok_access: { name: 'TikTok Access Test', description: 'Direct TikTok bot detection test' }\n  };\n\n  const runAntidetectTest = async () => {\n    setIsRunning(true);\n    setTestResults(null);\n\n    try {\n      const selectedSiteKeys = Object.keys(selectedTests).filter(key =>\n        selectedTests[key] && key !== 'tiktok_access'\n      );\n\n      const result = await antidetectAPI.testProfile(\n        profileId,\n        selectedSiteKeys,\n        selectedTests.tiktok_access,\n        testOptions.headless\n      );\n\n      setTestResults(result);\n    } catch (error) {\n      console.error('Antidetect test failed:', error);\n      const apiError = handleApiError(error);\n      setTestResults({\n        success: false,\n        error: apiError.message,\n        overall_score: 0,\n        test_results: {},\n        recommendations: ['Failed to run test: ' + apiError.message]\n      });\n    } finally {\n      setIsRunning(false);\n    }\n  };\n\n  const getScoreColor = (score) => {\n    if (score >= 80) return 'text-green-600';\n    if (score >= 60) return 'text-yellow-600';\n    return 'text-red-600';\n  };\n\n  const getScoreIcon = (score) => {\n    if (score >= 80) return <FiCheck className=\"text-green-600\" />;\n    if (score >= 60) return <FiAlertTriangle className=\"text-yellow-600\" />;\n    return <FiX className=\"text-red-600\" />;\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b\">\n          <div className=\"flex items-center space-x-3\">\n            <FiShield className=\"text-blue-600 text-xl\" />\n            <h2 className=\"text-xl font-semibold\">Antidetect Testing</h2>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <FiX size={24} />\n          </button>\n        </div>\n\n        <div className=\"p-6\">\n          {/* Test Configuration */}\n          <div className=\"mb-6\">\n            <h3 className=\"text-lg font-medium mb-4\">Test Configuration</h3>\n            \n            {/* Test Sites Selection */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\n              {Object.entries(testSites).map(([key, site]) => (\n                <label key={key} className=\"flex items-start space-x-3 p-3 border rounded-lg hover:bg-gray-50\">\n                  <input\n                    type=\"checkbox\"\n                    checked={selectedTests[key]}\n                    onChange={(e) => setSelectedTests(prev => ({\n                      ...prev,\n                      [key]: e.target.checked\n                    }))}\n                    className=\"mt-1\"\n                  />\n                  <div>\n                    <div className=\"font-medium\">{site.name}</div>\n                    <div className=\"text-sm text-gray-600\">{site.description}</div>\n                  </div>\n                </label>\n              ))}\n            </div>\n\n            {/* Test Options */}\n            <div className=\"flex items-center space-x-6\">\n              <label className=\"flex items-center space-x-2\">\n                <input\n                  type=\"checkbox\"\n                  checked={testOptions.headless}\n                  onChange={(e) => setTestOptions(prev => ({\n                    ...prev,\n                    headless: e.target.checked\n                  }))}\n                />\n                <span>Run in headless mode</span>\n              </label>\n            </div>\n          </div>\n\n          {/* Run Test Button */}\n          <div className=\"mb-6\">\n            <button\n              onClick={runAntidetectTest}\n              disabled={isRunning || Object.values(selectedTests).every(v => !v)}\n              className=\"flex items-center space-x-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isRunning ? (\n                <>\n                  <FiLoader className=\"animate-spin\" />\n                  <span>Running Tests...</span>\n                </>\n              ) : (\n                <>\n                  <FiPlay />\n                  <span>Run Antidetect Test</span>\n                </>\n              )}\n            </button>\n          </div>\n\n          {/* Test Results */}\n          {testResults && (\n            <div className=\"space-y-6\">\n              {/* Overall Score */}\n              <div className=\"bg-gray-50 p-4 rounded-lg\">\n                <div className=\"flex items-center justify-between\">\n                  <h3 className=\"text-lg font-medium\">Overall Detection Score</h3>\n                  <div className=\"flex items-center space-x-2\">\n                    {getScoreIcon(testResults.overall_score)}\n                    <span className={`text-2xl font-bold ${getScoreColor(testResults.overall_score)}`}>\n                      {testResults.overall_score}%\n                    </span>\n                  </div>\n                </div>\n                <div className=\"mt-2\">\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div\n                      className={`h-2 rounded-full ${\n                        testResults.overall_score >= 80 ? 'bg-green-600' :\n                        testResults.overall_score >= 60 ? 'bg-yellow-600' : 'bg-red-600'\n                      }`}\n                      style={{ width: `${testResults.overall_score}%` }}\n                    ></div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Individual Test Results */}\n              {testResults.success && testResults.test_results && (\n                <div>\n                  <h3 className=\"text-lg font-medium mb-4\">Test Results</h3>\n                  <div className=\"space-y-4\">\n                    {Object.entries(testResults.test_results).map(([testKey, result]) => (\n                      <div key={testKey} className=\"border rounded-lg p-4\">\n                        <div className=\"flex items-center justify-between mb-2\">\n                          <h4 className=\"font-medium\">\n                            {testSites[testKey]?.name || testKey}\n                          </h4>\n                          <div className=\"flex items-center space-x-2\">\n                            {result.success ? getScoreIcon(result.score || 0) : <FiX className=\"text-red-600\" />}\n                            <span className={`font-bold ${getScoreColor(result.score || 0)}`}>\n                              {result.score ? `${result.score}%` : 'Failed'}\n                            </span>\n                          </div>\n                        </div>\n                        \n                        {result.success && result.check_results && (\n                          <div className=\"text-sm text-gray-600\">\n                            <div>Checks passed: {result.checks_passed}/{result.total_checks}</div>\n                            {result.detection_indicators && result.detection_indicators.length > 0 && (\n                              <div className=\"text-red-600 mt-1\">\n                                Issues: {result.detection_indicators.join(', ')}\n                              </div>\n                            )}\n                          </div>\n                        )}\n                        \n                        {result.error && (\n                          <div className=\"text-red-600 text-sm\">\n                            Error: {result.error}\n                          </div>\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {/* Recommendations */}\n              {testResults.recommendations && testResults.recommendations.length > 0 && (\n                <div>\n                  <h3 className=\"text-lg font-medium mb-4\">Recommendations</h3>\n                  <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n                    <ul className=\"space-y-2\">\n                      {testResults.recommendations.map((recommendation, index) => (\n                        <li key={index} className=\"flex items-start space-x-2\">\n                          <span className=\"text-blue-600 mt-1\">•</span>\n                          <span className=\"text-blue-800\">{recommendation}</span>\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                </div>\n              )}\n\n              {/* Error Display */}\n              {!testResults.success && (\n                <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n                  <div className=\"flex items-center space-x-2 text-red-800\">\n                    <FiX />\n                    <span className=\"font-medium\">Test Failed</span>\n                  </div>\n                  <div className=\"text-red-700 mt-2\">\n                    {testResults.error || 'Unknown error occurred'}\n                  </div>\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AntidetectTester;\n"], "mappings": ";;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,OAAO,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,eAAe,QAAQ,gBAAgB;AAC1F,SAASC,aAAa,EAAEC,cAAc,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnE,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACnD,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC;IACjDuB,aAAa,EAAE,IAAI;IACnBC,eAAe,EAAE,IAAI;IACrBC,cAAc,EAAE,IAAI;IACpBC,SAAS,EAAE,IAAI;IACfC,mBAAmB,EAAE,IAAI;IACzBC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC;IAC7C+B,QAAQ,EAAE,KAAK;IACfC,aAAa,EAAE;EACjB,CAAC,CAAC;EAEF,MAAMC,SAAS,GAAG;IAChBV,aAAa,EAAE;MAAEW,IAAI,EAAE,wBAAwB;MAAEC,WAAW,EAAE;IAAoC,CAAC;IACnGX,eAAe,EAAE;MAAEU,IAAI,EAAE,sBAAsB;MAAEC,WAAW,EAAE;IAA6B,CAAC;IAC5FV,cAAc,EAAE;MAAES,IAAI,EAAE,kBAAkB;MAAEC,WAAW,EAAE;IAA8B,CAAC;IACxFT,SAAS,EAAE;MAAEQ,IAAI,EAAE,uBAAuB;MAAEC,WAAW,EAAE;IAAkC,CAAC;IAC5FR,mBAAmB,EAAE;MAAEO,IAAI,EAAE,qBAAqB;MAAEC,WAAW,EAAE;IAAwB,CAAC;IAC1FP,aAAa,EAAE;MAAEM,IAAI,EAAE,oBAAoB;MAAEC,WAAW,EAAE;IAAmC;EAC/F,CAAC;EAED,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpClB,YAAY,CAAC,IAAI,CAAC;IAClBE,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF,MAAMiB,gBAAgB,GAAGC,MAAM,CAACC,IAAI,CAAClB,aAAa,CAAC,CAACmB,MAAM,CAACC,GAAG,IAC5DpB,aAAa,CAACoB,GAAG,CAAC,IAAIA,GAAG,KAAK,eAChC,CAAC;MAED,MAAMC,MAAM,GAAG,MAAMnC,aAAa,CAACoC,WAAW,CAC5C7B,SAAS,EACTuB,gBAAgB,EAChBhB,aAAa,CAACO,aAAa,EAC3BC,WAAW,CAACE,QACd,CAAC;MAEDX,cAAc,CAACsB,MAAM,CAAC;IACxB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAME,QAAQ,GAAGtC,cAAc,CAACoC,KAAK,CAAC;MACtCxB,cAAc,CAAC;QACb2B,OAAO,EAAE,KAAK;QACdH,KAAK,EAAEE,QAAQ,CAACE,OAAO;QACvBC,aAAa,EAAE,CAAC;QAChBC,YAAY,EAAE,CAAC,CAAC;QAChBC,eAAe,EAAE,CAAC,sBAAsB,GAAGL,QAAQ,CAACE,OAAO;MAC7D,CAAC,CAAC;IACJ,CAAC,SAAS;MACR9B,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMkC,aAAa,GAAIC,KAAK,IAAK;IAC/B,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,gBAAgB;IACxC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,iBAAiB;IACzC,OAAO,cAAc;EACvB,CAAC;EAED,MAAMC,YAAY,GAAID,KAAK,IAAK;IAC9B,IAAIA,KAAK,IAAI,EAAE,EAAE,oBAAO3C,OAAA,CAACR,OAAO;MAACqD,SAAS,EAAC;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC9D,IAAIN,KAAK,IAAI,EAAE,EAAE,oBAAO3C,OAAA,CAACJ,eAAe;MAACiD,SAAS,EAAC;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvE,oBAAOjD,OAAA,CAACP,GAAG;MAACoD,SAAS,EAAC;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACzC,CAAC;EAED,oBACEjD,OAAA;IAAK6C,SAAS,EAAC,4EAA4E;IAAAK,QAAA,eACzFlD,OAAA;MAAK6C,SAAS,EAAC,kFAAkF;MAAAK,QAAA,gBAE/FlD,OAAA;QAAK6C,SAAS,EAAC,gDAAgD;QAAAK,QAAA,gBAC7DlD,OAAA;UAAK6C,SAAS,EAAC,6BAA6B;UAAAK,QAAA,gBAC1ClD,OAAA,CAACL,QAAQ;YAACkD,SAAS,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9CjD,OAAA;YAAI6C,SAAS,EAAC,uBAAuB;YAAAK,QAAA,EAAC;UAAkB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACNjD,OAAA;UACEmD,OAAO,EAAE9C,OAAQ;UACjBwC,SAAS,EAAC,mCAAmC;UAAAK,QAAA,eAE7ClD,OAAA,CAACP,GAAG;YAAC2D,IAAI,EAAE;UAAG;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENjD,OAAA;QAAK6C,SAAS,EAAC,KAAK;QAAAK,QAAA,gBAElBlD,OAAA;UAAK6C,SAAS,EAAC,MAAM;UAAAK,QAAA,gBACnBlD,OAAA;YAAI6C,SAAS,EAAC,0BAA0B;YAAAK,QAAA,EAAC;UAAkB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAGhEjD,OAAA;YAAK6C,SAAS,EAAC,4CAA4C;YAAAK,QAAA,EACxDtB,MAAM,CAACyB,OAAO,CAAC9B,SAAS,CAAC,CAAC+B,GAAG,CAAC,CAAC,CAACvB,GAAG,EAAEwB,IAAI,CAAC,kBACzCvD,OAAA;cAAiB6C,SAAS,EAAC,mEAAmE;cAAAK,QAAA,gBAC5FlD,OAAA;gBACEwD,IAAI,EAAC,UAAU;gBACfC,OAAO,EAAE9C,aAAa,CAACoB,GAAG,CAAE;gBAC5B2B,QAAQ,EAAGC,CAAC,IAAK/C,gBAAgB,CAACgD,IAAI,KAAK;kBACzC,GAAGA,IAAI;kBACP,CAAC7B,GAAG,GAAG4B,CAAC,CAACE,MAAM,CAACJ;gBAClB,CAAC,CAAC,CAAE;gBACJZ,SAAS,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACFjD,OAAA;gBAAAkD,QAAA,gBACElD,OAAA;kBAAK6C,SAAS,EAAC,aAAa;kBAAAK,QAAA,EAAEK,IAAI,CAAC/B;gBAAI;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9CjD,OAAA;kBAAK6C,SAAS,EAAC,uBAAuB;kBAAAK,QAAA,EAAEK,IAAI,CAAC9B;gBAAW;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC;YAAA,GAbIlB,GAAG;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAcR,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNjD,OAAA;YAAK6C,SAAS,EAAC,6BAA6B;YAAAK,QAAA,eAC1ClD,OAAA;cAAO6C,SAAS,EAAC,6BAA6B;cAAAK,QAAA,gBAC5ClD,OAAA;gBACEwD,IAAI,EAAC,UAAU;gBACfC,OAAO,EAAEtC,WAAW,CAACE,QAAS;gBAC9BqC,QAAQ,EAAGC,CAAC,IAAKvC,cAAc,CAACwC,IAAI,KAAK;kBACvC,GAAGA,IAAI;kBACPvC,QAAQ,EAAEsC,CAAC,CAACE,MAAM,CAACJ;gBACrB,CAAC,CAAC;cAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACFjD,OAAA;gBAAAkD,QAAA,EAAM;cAAoB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNjD,OAAA;UAAK6C,SAAS,EAAC,MAAM;UAAAK,QAAA,eACnBlD,OAAA;YACEmD,OAAO,EAAEzB,iBAAkB;YAC3BoC,QAAQ,EAAEvD,SAAS,IAAIqB,MAAM,CAACmC,MAAM,CAACpD,aAAa,CAAC,CAACqD,KAAK,CAACC,CAAC,IAAI,CAACA,CAAC,CAAE;YACnEpB,SAAS,EAAC,2IAA2I;YAAAK,QAAA,EAEpJ3C,SAAS,gBACRP,OAAA,CAAAE,SAAA;cAAAgD,QAAA,gBACElD,OAAA,CAACN,QAAQ;gBAACmD,SAAS,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrCjD,OAAA;gBAAAkD,QAAA,EAAM;cAAgB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eAC7B,CAAC,gBAEHjD,OAAA,CAAAE,SAAA;cAAAgD,QAAA,gBACElD,OAAA,CAACT,MAAM;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACVjD,OAAA;gBAAAkD,QAAA,EAAM;cAAmB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,eAChC;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGLxC,WAAW,iBACVT,OAAA;UAAK6C,SAAS,EAAC,WAAW;UAAAK,QAAA,gBAExBlD,OAAA;YAAK6C,SAAS,EAAC,2BAA2B;YAAAK,QAAA,gBACxClD,OAAA;cAAK6C,SAAS,EAAC,mCAAmC;cAAAK,QAAA,gBAChDlD,OAAA;gBAAI6C,SAAS,EAAC,qBAAqB;gBAAAK,QAAA,EAAC;cAAuB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChEjD,OAAA;gBAAK6C,SAAS,EAAC,6BAA6B;gBAAAK,QAAA,GACzCN,YAAY,CAACnC,WAAW,CAAC8B,aAAa,CAAC,eACxCvC,OAAA;kBAAM6C,SAAS,EAAE,sBAAsBH,aAAa,CAACjC,WAAW,CAAC8B,aAAa,CAAC,EAAG;kBAAAW,QAAA,GAC/EzC,WAAW,CAAC8B,aAAa,EAAC,GAC7B;gBAAA;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjD,OAAA;cAAK6C,SAAS,EAAC,MAAM;cAAAK,QAAA,eACnBlD,OAAA;gBAAK6C,SAAS,EAAC,qCAAqC;gBAAAK,QAAA,eAClDlD,OAAA;kBACE6C,SAAS,EAAE,oBACTpC,WAAW,CAAC8B,aAAa,IAAI,EAAE,GAAG,cAAc,GAChD9B,WAAW,CAAC8B,aAAa,IAAI,EAAE,GAAG,eAAe,GAAG,YAAY,EAC/D;kBACH2B,KAAK,EAAE;oBAAEC,KAAK,EAAE,GAAG1D,WAAW,CAAC8B,aAAa;kBAAI;gBAAE;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLxC,WAAW,CAAC4B,OAAO,IAAI5B,WAAW,CAAC+B,YAAY,iBAC9CxC,OAAA;YAAAkD,QAAA,gBACElD,OAAA;cAAI6C,SAAS,EAAC,0BAA0B;cAAAK,QAAA,EAAC;YAAY;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1DjD,OAAA;cAAK6C,SAAS,EAAC,WAAW;cAAAK,QAAA,EACvBtB,MAAM,CAACyB,OAAO,CAAC5C,WAAW,CAAC+B,YAAY,CAAC,CAACc,GAAG,CAAC,CAAC,CAACc,OAAO,EAAEpC,MAAM,CAAC;gBAAA,IAAAqC,kBAAA;gBAAA,oBAC9DrE,OAAA;kBAAmB6C,SAAS,EAAC,uBAAuB;kBAAAK,QAAA,gBAClDlD,OAAA;oBAAK6C,SAAS,EAAC,wCAAwC;oBAAAK,QAAA,gBACrDlD,OAAA;sBAAI6C,SAAS,EAAC,aAAa;sBAAAK,QAAA,EACxB,EAAAmB,kBAAA,GAAA9C,SAAS,CAAC6C,OAAO,CAAC,cAAAC,kBAAA,uBAAlBA,kBAAA,CAAoB7C,IAAI,KAAI4C;oBAAO;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClC,CAAC,eACLjD,OAAA;sBAAK6C,SAAS,EAAC,6BAA6B;sBAAAK,QAAA,GACzClB,MAAM,CAACK,OAAO,GAAGO,YAAY,CAACZ,MAAM,CAACW,KAAK,IAAI,CAAC,CAAC,gBAAG3C,OAAA,CAACP,GAAG;wBAACoD,SAAS,EAAC;sBAAc;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACpFjD,OAAA;wBAAM6C,SAAS,EAAE,aAAaH,aAAa,CAACV,MAAM,CAACW,KAAK,IAAI,CAAC,CAAC,EAAG;wBAAAO,QAAA,EAC9DlB,MAAM,CAACW,KAAK,GAAG,GAAGX,MAAM,CAACW,KAAK,GAAG,GAAG;sBAAQ;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAELjB,MAAM,CAACK,OAAO,IAAIL,MAAM,CAACsC,aAAa,iBACrCtE,OAAA;oBAAK6C,SAAS,EAAC,uBAAuB;oBAAAK,QAAA,gBACpClD,OAAA;sBAAAkD,QAAA,GAAK,iBAAe,EAAClB,MAAM,CAACuC,aAAa,EAAC,GAAC,EAACvC,MAAM,CAACwC,YAAY;oBAAA;sBAAA1B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,EACrEjB,MAAM,CAACyC,oBAAoB,IAAIzC,MAAM,CAACyC,oBAAoB,CAACC,MAAM,GAAG,CAAC,iBACpE1E,OAAA;sBAAK6C,SAAS,EAAC,mBAAmB;sBAAAK,QAAA,GAAC,UACzB,EAAClB,MAAM,CAACyC,oBAAoB,CAACE,IAAI,CAAC,IAAI,CAAC;oBAAA;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CACN,EAEAjB,MAAM,CAACE,KAAK,iBACXlC,OAAA;oBAAK6C,SAAS,EAAC,sBAAsB;oBAAAK,QAAA,GAAC,SAC7B,EAAClB,MAAM,CAACE,KAAK;kBAAA;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CACN;gBAAA,GA5BOmB,OAAO;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA6BZ,CAAC;cAAA,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGAxC,WAAW,CAACgC,eAAe,IAAIhC,WAAW,CAACgC,eAAe,CAACiC,MAAM,GAAG,CAAC,iBACpE1E,OAAA;YAAAkD,QAAA,gBACElD,OAAA;cAAI6C,SAAS,EAAC,0BAA0B;cAAAK,QAAA,EAAC;YAAe;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7DjD,OAAA;cAAK6C,SAAS,EAAC,kDAAkD;cAAAK,QAAA,eAC/DlD,OAAA;gBAAI6C,SAAS,EAAC,WAAW;gBAAAK,QAAA,EACtBzC,WAAW,CAACgC,eAAe,CAACa,GAAG,CAAC,CAACsB,cAAc,EAAEC,KAAK,kBACrD7E,OAAA;kBAAgB6C,SAAS,EAAC,4BAA4B;kBAAAK,QAAA,gBACpDlD,OAAA;oBAAM6C,SAAS,EAAC,oBAAoB;oBAAAK,QAAA,EAAC;kBAAC;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7CjD,OAAA;oBAAM6C,SAAS,EAAC,eAAe;oBAAAK,QAAA,EAAE0B;kBAAc;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAFhD4B,KAAK;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGV,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGA,CAACxC,WAAW,CAAC4B,OAAO,iBACnBrC,OAAA;YAAK6C,SAAS,EAAC,gDAAgD;YAAAK,QAAA,gBAC7DlD,OAAA;cAAK6C,SAAS,EAAC,0CAA0C;cAAAK,QAAA,gBACvDlD,OAAA,CAACP,GAAG;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACPjD,OAAA;gBAAM6C,SAAS,EAAC,aAAa;gBAAAK,QAAA,EAAC;cAAW;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACNjD,OAAA;cAAK6C,SAAS,EAAC,mBAAmB;cAAAK,QAAA,EAC/BzC,WAAW,CAACyB,KAAK,IAAI;YAAwB;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3C,EAAA,CA5PIH,gBAAgB;AAAA2E,EAAA,GAAhB3E,gBAAgB;AA8PtB,eAAeA,gBAAgB;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}