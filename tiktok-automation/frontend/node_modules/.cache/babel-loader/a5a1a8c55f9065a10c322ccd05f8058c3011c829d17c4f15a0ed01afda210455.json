{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Dashboard/RecentActivity.jsx\",\n  _s = $RefreshSig$();\n/**\n * Recent Activity Component for Dashboard\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { FiUser, FiUserPlus, FiUserMinus, FiPlay, FiPause, FiSettings, FiGlobe, FiClock } from 'react-icons/fi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RecentActivity = ({\n  activities: propActivities = []\n}) => {\n  _s();\n  const [activities, setActivities] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Use prop activities if provided, otherwise use mock data\n  useEffect(() => {\n    if (propActivities.length > 0) {\n      // Convert log entries to activity format\n      const convertedActivities = propActivities.map((log, index) => ({\n        id: log.id || index,\n        type: log.level === 'ERROR' ? 'error' : 'info',\n        title: log.level === 'ERROR' ? 'Error Occurred' : 'System Event',\n        description: log.message,\n        timestamp: new Date(Date.now() - index * 60 * 1000),\n        // Spread out timestamps\n        icon: log.level === 'ERROR' ? FiPause : FiPlay,\n        color: log.level === 'ERROR' ? 'red' : 'green'\n      }));\n      setActivities(convertedActivities);\n      setLoading(false);\n      return;\n    }\n\n    // Mock activity data - in real app this would come from API\n    const mockActivities = [{\n      id: 1,\n      type: 'task_started',\n      title: 'Task Started',\n      description: 'Follow task for @competitor1 started',\n      timestamp: new Date(Date.now() - 5 * 60 * 1000),\n      // 5 minutes ago\n      icon: FiPlay,\n      color: 'green'\n    }, {\n      id: 2,\n      type: 'user_followed',\n      title: 'User Followed',\n      description: 'Successfully followed @user123',\n      timestamp: new Date(Date.now() - 12 * 60 * 1000),\n      // 12 minutes ago\n      icon: FiUserPlus,\n      color: 'blue'\n    }, {\n      id: 3,\n      type: 'profile_created',\n      title: 'Profile Created',\n      description: 'New browser profile \"Profile 5\" created',\n      timestamp: new Date(Date.now() - 25 * 60 * 1000),\n      // 25 minutes ago\n      icon: FiUser,\n      color: 'purple'\n    }, {\n      id: 4,\n      type: 'proxy_added',\n      title: 'Proxy Added',\n      description: 'New proxy server ************* configured',\n      timestamp: new Date(Date.now() - 45 * 60 * 1000),\n      // 45 minutes ago\n      icon: FiGlobe,\n      color: 'orange'\n    }, {\n      id: 5,\n      type: 'task_paused',\n      title: 'Task Paused',\n      description: 'Unfollow task paused due to rate limit',\n      timestamp: new Date(Date.now() - 60 * 60 * 1000),\n      // 1 hour ago\n      icon: FiPause,\n      color: 'yellow'\n    }, {\n      id: 6,\n      type: 'user_unfollowed',\n      title: 'User Unfollowed',\n      description: 'Unfollowed @inactive_user456',\n      timestamp: new Date(Date.now() - 75 * 60 * 1000),\n      // 1 hour 15 minutes ago\n      icon: FiUserMinus,\n      color: 'red'\n    }, {\n      id: 7,\n      type: 'settings_updated',\n      title: 'Settings Updated',\n      description: 'Rate limit settings modified',\n      timestamp: new Date(Date.now() - 90 * 60 * 1000),\n      // 1 hour 30 minutes ago\n      icon: FiSettings,\n      color: 'gray'\n    }];\n\n    // Simulate loading delay\n    setTimeout(() => {\n      setActivities(mockActivities);\n      setLoading(false);\n    }, 1000);\n  }, []);\n  const getTimeAgo = timestamp => {\n    const now = new Date();\n    const diff = now - timestamp;\n    const minutes = Math.floor(diff / 60000);\n    const hours = Math.floor(minutes / 60);\n    const days = Math.floor(hours / 24);\n    if (days > 0) {\n      return `${days} day${days > 1 ? 's' : ''} ago`;\n    } else if (hours > 0) {\n      return `${hours} hour${hours > 1 ? 's' : ''} ago`;\n    } else if (minutes > 0) {\n      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;\n    } else {\n      return 'Just now';\n    }\n  };\n  const getColorClasses = color => {\n    const colorMap = {\n      green: 'bg-green-100 text-green-600',\n      blue: 'bg-blue-100 text-blue-600',\n      purple: 'bg-purple-100 text-purple-600',\n      orange: 'bg-orange-100 text-orange-600',\n      yellow: 'bg-yellow-100 text-yellow-600',\n      red: 'bg-red-100 text-red-600',\n      gray: 'bg-gray-100 text-gray-600'\n    };\n    return colorMap[color] || colorMap.gray;\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-lg font-semibold text-gray-900 mb-4\",\n        children: \"Recent Activity\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [1, 2, 3, 4, 5].map(i => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-pulse flex items-start space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 bg-gray-200 rounded-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-3 bg-gray-200 rounded w-1/2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-3 bg-gray-200 rounded w-16\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 15\n          }, this)]\n        }, i, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-lg font-semibold text-gray-900\",\n        children: \"Recent Activity\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"text-sm text-blue-600 hover:text-blue-800 font-medium\",\n        children: \"View All\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this), activities.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-8\",\n      children: [/*#__PURE__*/_jsxDEV(FiClock, {\n        className: \"w-12 h-12 text-gray-300 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-2\",\n        children: \"No Recent Activity\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"Activity will appear here as you use the system.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: activities.map((activity, index) => {\n        const Icon = activity.icon;\n        return /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: -20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            delay: index * 0.05\n          },\n          className: \"flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `w-8 h-8 rounded-full flex items-center justify-center ${getColorClasses(activity.color)}`,\n            children: /*#__PURE__*/_jsxDEV(Icon, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-medium text-gray-900 truncate\",\n                children: activity.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs text-gray-500 whitespace-nowrap ml-2\",\n                children: getTimeAgo(activity.timestamp)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600 mt-1 truncate\",\n              children: activity.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 17\n          }, this)]\n        }, activity.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 15\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6 pt-4 border-t border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-3 gap-4 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: activities.filter(a => a.type.includes('task')).length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600\",\n            children: \"Tasks\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: activities.filter(a => a.type.includes('user')).length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600\",\n            children: \"User Actions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: activities.filter(a => a.type.includes('profile') || a.type.includes('proxy')).length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600\",\n            children: \"Config Changes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 166,\n    columnNumber: 5\n  }, this);\n};\n_s(RecentActivity, \"C8uifjA9jREC8y+qhfs5+cS/Q5A=\");\n_c = RecentActivity;\nexport default RecentActivity;\nvar _c;\n$RefreshReg$(_c, \"RecentActivity\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "FiUser", "FiUserPlus", "FiUserMinus", "FiPlay", "FiPause", "FiSettings", "FiGlobe", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "RecentActivity", "activities", "propActivities", "_s", "setActivities", "loading", "setLoading", "length", "convertedActivities", "map", "log", "index", "id", "type", "level", "title", "description", "message", "timestamp", "Date", "now", "icon", "color", "mockActivities", "setTimeout", "getTimeAgo", "diff", "minutes", "Math", "floor", "hours", "days", "getColorClasses", "colorMap", "green", "blue", "purple", "orange", "yellow", "red", "gray", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "i", "activity", "Icon", "div", "initial", "opacity", "x", "animate", "transition", "delay", "filter", "a", "includes", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Dashboard/RecentActivity.jsx"], "sourcesContent": ["/**\n * Recent Activity Component for Dashboard\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  FiUser,\n  FiUserPlus,\n  FiUserMinus,\n  FiPlay,\n  FiPause,\n  FiSettings,\n  FiGlobe,\n  FiClock\n} from 'react-icons/fi';\n\nconst RecentActivity = ({ activities: propActivities = [] }) => {\n  const [activities, setActivities] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Use prop activities if provided, otherwise use mock data\n  useEffect(() => {\n    if (propActivities.length > 0) {\n      // Convert log entries to activity format\n      const convertedActivities = propActivities.map((log, index) => ({\n        id: log.id || index,\n        type: log.level === 'ERROR' ? 'error' : 'info',\n        title: log.level === 'ERROR' ? 'Error Occurred' : 'System Event',\n        description: log.message,\n        timestamp: new Date(Date.now() - index * 60 * 1000), // Spread out timestamps\n        icon: log.level === 'ERROR' ? FiPause : FiPlay,\n        color: log.level === 'ERROR' ? 'red' : 'green'\n      }));\n      setActivities(convertedActivities);\n      setLoading(false);\n      return;\n    }\n\n    // Mock activity data - in real app this would come from API\n    const mockActivities = [\n      {\n        id: 1,\n        type: 'task_started',\n        title: 'Task Started',\n        description: 'Follow task for @competitor1 started',\n        timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago\n        icon: FiPlay,\n        color: 'green'\n      },\n      {\n        id: 2,\n        type: 'user_followed',\n        title: 'User Followed',\n        description: 'Successfully followed @user123',\n        timestamp: new Date(Date.now() - 12 * 60 * 1000), // 12 minutes ago\n        icon: FiUserPlus,\n        color: 'blue'\n      },\n      {\n        id: 3,\n        type: 'profile_created',\n        title: 'Profile Created',\n        description: 'New browser profile \"Profile 5\" created',\n        timestamp: new Date(Date.now() - 25 * 60 * 1000), // 25 minutes ago\n        icon: FiUser,\n        color: 'purple'\n      },\n      {\n        id: 4,\n        type: 'proxy_added',\n        title: 'Proxy Added',\n        description: 'New proxy server ************* configured',\n        timestamp: new Date(Date.now() - 45 * 60 * 1000), // 45 minutes ago\n        icon: FiGlobe,\n        color: 'orange'\n      },\n      {\n        id: 5,\n        type: 'task_paused',\n        title: 'Task Paused',\n        description: 'Unfollow task paused due to rate limit',\n        timestamp: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago\n        icon: FiPause,\n        color: 'yellow'\n      },\n      {\n        id: 6,\n        type: 'user_unfollowed',\n        title: 'User Unfollowed',\n        description: 'Unfollowed @inactive_user456',\n        timestamp: new Date(Date.now() - 75 * 60 * 1000), // 1 hour 15 minutes ago\n        icon: FiUserMinus,\n        color: 'red'\n      },\n      {\n        id: 7,\n        type: 'settings_updated',\n        title: 'Settings Updated',\n        description: 'Rate limit settings modified',\n        timestamp: new Date(Date.now() - 90 * 60 * 1000), // 1 hour 30 minutes ago\n        icon: FiSettings,\n        color: 'gray'\n      }\n    ];\n\n    // Simulate loading delay\n    setTimeout(() => {\n      setActivities(mockActivities);\n      setLoading(false);\n    }, 1000);\n  }, []);\n\n  const getTimeAgo = (timestamp) => {\n    const now = new Date();\n    const diff = now - timestamp;\n    const minutes = Math.floor(diff / 60000);\n    const hours = Math.floor(minutes / 60);\n    const days = Math.floor(hours / 24);\n\n    if (days > 0) {\n      return `${days} day${days > 1 ? 's' : ''} ago`;\n    } else if (hours > 0) {\n      return `${hours} hour${hours > 1 ? 's' : ''} ago`;\n    } else if (minutes > 0) {\n      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;\n    } else {\n      return 'Just now';\n    }\n  };\n\n  const getColorClasses = (color) => {\n    const colorMap = {\n      green: 'bg-green-100 text-green-600',\n      blue: 'bg-blue-100 text-blue-600',\n      purple: 'bg-purple-100 text-purple-600',\n      orange: 'bg-orange-100 text-orange-600',\n      yellow: 'bg-yellow-100 text-yellow-600',\n      red: 'bg-red-100 text-red-600',\n      gray: 'bg-gray-100 text-gray-600'\n    };\n    return colorMap[color] || colorMap.gray;\n  };\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Recent Activity</h2>\n        <div className=\"space-y-4\">\n          {[1, 2, 3, 4, 5].map((i) => (\n            <div key={i} className=\"animate-pulse flex items-start space-x-3\">\n              <div className=\"w-8 h-8 bg-gray-200 rounded-full\"></div>\n              <div className=\"flex-1\">\n                <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n                <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n              </div>\n              <div className=\"h-3 bg-gray-200 rounded w-16\"></div>\n            </div>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900\">Recent Activity</h2>\n        <button className=\"text-sm text-blue-600 hover:text-blue-800 font-medium\">\n          View All\n        </button>\n      </div>\n\n      {activities.length === 0 ? (\n        <div className=\"text-center py-8\">\n          <FiClock className=\"w-12 h-12 text-gray-300 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No Recent Activity</h3>\n          <p className=\"text-gray-600\">Activity will appear here as you use the system.</p>\n        </div>\n      ) : (\n        <div className=\"space-y-4\">\n          {activities.map((activity, index) => {\n            const Icon = activity.icon;\n            return (\n              <motion.div\n                key={activity.id}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ delay: index * 0.05 }}\n                className=\"flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors\"\n              >\n                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${getColorClasses(activity.color)}`}>\n                  <Icon className=\"w-4 h-4\" />\n                </div>\n                \n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"flex items-center justify-between\">\n                    <h3 className=\"font-medium text-gray-900 truncate\">\n                      {activity.title}\n                    </h3>\n                    <span className=\"text-xs text-gray-500 whitespace-nowrap ml-2\">\n                      {getTimeAgo(activity.timestamp)}\n                    </span>\n                  </div>\n                  <p className=\"text-sm text-gray-600 mt-1 truncate\">\n                    {activity.description}\n                  </p>\n                </div>\n              </motion.div>\n            );\n          })}\n        </div>\n      )}\n\n      {/* Activity Summary */}\n      <div className=\"mt-6 pt-4 border-t border-gray-200\">\n        <div className=\"grid grid-cols-3 gap-4 text-center\">\n          <div>\n            <div className=\"text-lg font-semibold text-gray-900\">\n              {activities.filter(a => a.type.includes('task')).length}\n            </div>\n            <div className=\"text-sm text-gray-600\">Tasks</div>\n          </div>\n          <div>\n            <div className=\"text-lg font-semibold text-gray-900\">\n              {activities.filter(a => a.type.includes('user')).length}\n            </div>\n            <div className=\"text-sm text-gray-600\">User Actions</div>\n          </div>\n          <div>\n            <div className=\"text-lg font-semibold text-gray-900\">\n              {activities.filter(a => a.type.includes('profile') || a.type.includes('proxy')).length}\n            </div>\n            <div className=\"text-sm text-gray-600\">Config Changes</div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RecentActivity;\n"], "mappings": ";;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,MAAM,EACNC,UAAU,EACVC,WAAW,EACXC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,OAAO,EACPC,OAAO,QACF,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,cAAc,GAAGA,CAAC;EAAEC,UAAU,EAAEC,cAAc,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EAC9D,MAAM,CAACF,UAAU,EAAEG,aAAa,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,IAAIc,cAAc,CAACK,MAAM,GAAG,CAAC,EAAE;MAC7B;MACA,MAAMC,mBAAmB,GAAGN,cAAc,CAACO,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,MAAM;QAC9DC,EAAE,EAAEF,GAAG,CAACE,EAAE,IAAID,KAAK;QACnBE,IAAI,EAAEH,GAAG,CAACI,KAAK,KAAK,OAAO,GAAG,OAAO,GAAG,MAAM;QAC9CC,KAAK,EAAEL,GAAG,CAACI,KAAK,KAAK,OAAO,GAAG,gBAAgB,GAAG,cAAc;QAChEE,WAAW,EAAEN,GAAG,CAACO,OAAO;QACxBC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGT,KAAK,GAAG,EAAE,GAAG,IAAI,CAAC;QAAE;QACrDU,IAAI,EAAEX,GAAG,CAACI,KAAK,KAAK,OAAO,GAAGpB,OAAO,GAAGD,MAAM;QAC9C6B,KAAK,EAAEZ,GAAG,CAACI,KAAK,KAAK,OAAO,GAAG,KAAK,GAAG;MACzC,CAAC,CAAC,CAAC;MACHV,aAAa,CAACI,mBAAmB,CAAC;MAClCF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;;IAEA;IACA,MAAMiB,cAAc,GAAG,CACrB;MACEX,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,cAAc;MACpBE,KAAK,EAAE,cAAc;MACrBC,WAAW,EAAE,sCAAsC;MACnDE,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;MAAE;MACjDC,IAAI,EAAE5B,MAAM;MACZ6B,KAAK,EAAE;IACT,CAAC,EACD;MACEV,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,eAAe;MACrBE,KAAK,EAAE,eAAe;MACtBC,WAAW,EAAE,gCAAgC;MAC7CE,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAAE;MAClDC,IAAI,EAAE9B,UAAU;MAChB+B,KAAK,EAAE;IACT,CAAC,EACD;MACEV,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,iBAAiB;MACvBE,KAAK,EAAE,iBAAiB;MACxBC,WAAW,EAAE,yCAAyC;MACtDE,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAAE;MAClDC,IAAI,EAAE/B,MAAM;MACZgC,KAAK,EAAE;IACT,CAAC,EACD;MACEV,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,aAAa;MACnBE,KAAK,EAAE,aAAa;MACpBC,WAAW,EAAE,2CAA2C;MACxDE,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAAE;MAClDC,IAAI,EAAEzB,OAAO;MACb0B,KAAK,EAAE;IACT,CAAC,EACD;MACEV,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,aAAa;MACnBE,KAAK,EAAE,aAAa;MACpBC,WAAW,EAAE,wCAAwC;MACrDE,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAAE;MAClDC,IAAI,EAAE3B,OAAO;MACb4B,KAAK,EAAE;IACT,CAAC,EACD;MACEV,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,iBAAiB;MACvBE,KAAK,EAAE,iBAAiB;MACxBC,WAAW,EAAE,8BAA8B;MAC3CE,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAAE;MAClDC,IAAI,EAAE7B,WAAW;MACjB8B,KAAK,EAAE;IACT,CAAC,EACD;MACEV,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,kBAAkB;MACxBE,KAAK,EAAE,kBAAkB;MACzBC,WAAW,EAAE,8BAA8B;MAC3CE,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAAE;MAClDC,IAAI,EAAE1B,UAAU;MAChB2B,KAAK,EAAE;IACT,CAAC,CACF;;IAED;IACAE,UAAU,CAAC,MAAM;MACfpB,aAAa,CAACmB,cAAc,CAAC;MAC7BjB,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMmB,UAAU,GAAIP,SAAS,IAAK;IAChC,MAAME,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAMO,IAAI,GAAGN,GAAG,GAAGF,SAAS;IAC5B,MAAMS,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,IAAI,GAAG,KAAK,CAAC;IACxC,MAAMI,KAAK,GAAGF,IAAI,CAACC,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;IACtC,MAAMI,IAAI,GAAGH,IAAI,CAACC,KAAK,CAACC,KAAK,GAAG,EAAE,CAAC;IAEnC,IAAIC,IAAI,GAAG,CAAC,EAAE;MACZ,OAAO,GAAGA,IAAI,OAAOA,IAAI,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM;IAChD,CAAC,MAAM,IAAID,KAAK,GAAG,CAAC,EAAE;MACpB,OAAO,GAAGA,KAAK,QAAQA,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM;IACnD,CAAC,MAAM,IAAIH,OAAO,GAAG,CAAC,EAAE;MACtB,OAAO,GAAGA,OAAO,UAAUA,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM;IACzD,CAAC,MAAM;MACL,OAAO,UAAU;IACnB;EACF,CAAC;EAED,MAAMK,eAAe,GAAIV,KAAK,IAAK;IACjC,MAAMW,QAAQ,GAAG;MACfC,KAAK,EAAE,6BAA6B;MACpCC,IAAI,EAAE,2BAA2B;MACjCC,MAAM,EAAE,+BAA+B;MACvCC,MAAM,EAAE,+BAA+B;MACvCC,MAAM,EAAE,+BAA+B;MACvCC,GAAG,EAAE,yBAAyB;MAC9BC,IAAI,EAAE;IACR,CAAC;IACD,OAAOP,QAAQ,CAACX,KAAK,CAAC,IAAIW,QAAQ,CAACO,IAAI;EACzC,CAAC;EAED,IAAInC,OAAO,EAAE;IACX,oBACEN,OAAA;MAAK0C,SAAS,EAAC,0DAA0D;MAAAC,QAAA,gBACvE3C,OAAA;QAAI0C,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7E/C,OAAA;QAAK0C,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACjC,GAAG,CAAEsC,CAAC,iBACrBhD,OAAA;UAAa0C,SAAS,EAAC,0CAA0C;UAAAC,QAAA,gBAC/D3C,OAAA;YAAK0C,SAAS,EAAC;UAAkC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxD/C,OAAA;YAAK0C,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrB3C,OAAA;cAAK0C,SAAS,EAAC;YAAoC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1D/C,OAAA;cAAK0C,SAAS,EAAC;YAA+B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACN/C,OAAA;YAAK0C,SAAS,EAAC;UAA8B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GAN5CC,CAAC;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAON,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE/C,OAAA;IAAK0C,SAAS,EAAC,0DAA0D;IAAAC,QAAA,gBACvE3C,OAAA;MAAK0C,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrD3C,OAAA;QAAI0C,SAAS,EAAC,qCAAqC;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxE/C,OAAA;QAAQ0C,SAAS,EAAC,uDAAuD;QAAAC,QAAA,EAAC;MAE1E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAEL7C,UAAU,CAACM,MAAM,KAAK,CAAC,gBACtBR,OAAA;MAAK0C,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/B3C,OAAA,CAACF,OAAO;QAAC4C,SAAS,EAAC;MAAsC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5D/C,OAAA;QAAI0C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9E/C,OAAA;QAAG0C,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAgD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9E,CAAC,gBAEN/C,OAAA;MAAK0C,SAAS,EAAC,WAAW;MAAAC,QAAA,EACvBzC,UAAU,CAACQ,GAAG,CAAC,CAACuC,QAAQ,EAAErC,KAAK,KAAK;QACnC,MAAMsC,IAAI,GAAGD,QAAQ,CAAC3B,IAAI;QAC1B,oBACEtB,OAAA,CAACV,MAAM,CAAC6D,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,KAAK,EAAE7C,KAAK,GAAG;UAAK,CAAE;UACpC8B,SAAS,EAAC,8EAA8E;UAAAC,QAAA,gBAExF3C,OAAA;YAAK0C,SAAS,EAAE,yDAAyDT,eAAe,CAACgB,QAAQ,CAAC1B,KAAK,CAAC,EAAG;YAAAoB,QAAA,eACzG3C,OAAA,CAACkD,IAAI;cAACR,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eAEN/C,OAAA;YAAK0C,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B3C,OAAA;cAAK0C,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD3C,OAAA;gBAAI0C,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAC/CM,QAAQ,CAACjC;cAAK;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eACL/C,OAAA;gBAAM0C,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAC3DjB,UAAU,CAACuB,QAAQ,CAAC9B,SAAS;cAAC;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN/C,OAAA;cAAG0C,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAC/CM,QAAQ,CAAChC;YAAW;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA,GAtBDE,QAAQ,CAACpC,EAAE;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuBN,CAAC;MAEjB,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,eAGD/C,OAAA;MAAK0C,SAAS,EAAC,oCAAoC;MAAAC,QAAA,eACjD3C,OAAA;QAAK0C,SAAS,EAAC,oCAAoC;QAAAC,QAAA,gBACjD3C,OAAA;UAAA2C,QAAA,gBACE3C,OAAA;YAAK0C,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EACjDzC,UAAU,CAACwD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7C,IAAI,CAAC8C,QAAQ,CAAC,MAAM,CAAC,CAAC,CAACpD;UAAM;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACN/C,OAAA;YAAK0C,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACN/C,OAAA;UAAA2C,QAAA,gBACE3C,OAAA;YAAK0C,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EACjDzC,UAAU,CAACwD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7C,IAAI,CAAC8C,QAAQ,CAAC,MAAM,CAAC,CAAC,CAACpD;UAAM;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACN/C,OAAA;YAAK0C,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eACN/C,OAAA;UAAA2C,QAAA,gBACE3C,OAAA;YAAK0C,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EACjDzC,UAAU,CAACwD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7C,IAAI,CAAC8C,QAAQ,CAAC,SAAS,CAAC,IAAID,CAAC,CAAC7C,IAAI,CAAC8C,QAAQ,CAAC,OAAO,CAAC,CAAC,CAACpD;UAAM;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC,eACN/C,OAAA;YAAK0C,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3C,EAAA,CA9NIH,cAAc;AAAA4D,EAAA,GAAd5D,cAAc;AAgOpB,eAAeA,cAAc;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}