{"ast": null, "code": "/**\n * API Service for TikTok Automation Backend\n */\n\nimport axios from 'axios';\n\n// Get backend URL\nconst getBackendUrl = async () => {\n  if (window.electronAPI) {\n    return await window.electronAPI.getBackendUrl();\n  }\n  return 'http://localhost:8000';\n};\n\n// Create axios instance\nconst createApiInstance = async () => {\n  const baseURL = await getBackendUrl();\n  return axios.create({\n    baseURL: `${baseURL}/api/v1`,\n    timeout: 30000,\n    headers: {\n      'Content-Type': 'application/json'\n    }\n  });\n};\n\n// Profile API\nexport const profileAPI = {\n  // Get all profiles\n  async getProfiles(params = {}) {\n    const api = await createApiInstance();\n    const response = await api.get('/profiles/', {\n      params\n    });\n    return response.data;\n  },\n  // Get profile by ID\n  async getProfile(profileId) {\n    const api = await createApiInstance();\n    const response = await api.get(`/profiles/${profileId}`);\n    return response.data;\n  },\n  // Create new profile\n  async createProfile(profileData) {\n    const api = await createApiInstance();\n    const response = await api.post('/profiles/', profileData);\n    return response.data;\n  },\n  // Update profile\n  async updateProfile(profileId, updates) {\n    const api = await createApiInstance();\n    const response = await api.put(`/profiles/${profileId}`, updates);\n    return response.data;\n  },\n  // Delete profile\n  async deleteProfile(profileId) {\n    const api = await createApiInstance();\n    const response = await api.delete(`/profiles/${profileId}`);\n    return response.data;\n  },\n  // Duplicate profile\n  async duplicateProfile(profileId, newName, regenerateFingerprint = false) {\n    const api = await createApiInstance();\n    const response = await api.post(`/profiles/${profileId}/duplicate`, {\n      new_name: newName,\n      regenerate_fingerprint: regenerateFingerprint\n    });\n    return response.data;\n  },\n  // Test profile\n  async testProfile(profileId) {\n    const api = await createApiInstance();\n    const response = await api.post(`/profiles/${profileId}/test`);\n    return response.data;\n  },\n  // Get profile templates\n  async getTemplates() {\n    const api = await createApiInstance();\n    const response = await api.get('/profiles/templates/list');\n    return response.data;\n  },\n  // Create from template\n  async createFromTemplate(templateName, profileName, proxyId = null) {\n    const api = await createApiInstance();\n    const response = await api.post(`/profiles/templates/${templateName}`, {\n      profile_name: profileName,\n      proxy_id: proxyId\n    });\n    return response.data;\n  }\n};\n\n// Proxy API\nexport const proxyAPI = {\n  // Get all proxies\n  async getProxies(params = {}) {\n    const api = await createApiInstance();\n    const response = await api.get('/proxies/', {\n      params\n    });\n    return response.data;\n  },\n  // Get proxy by ID\n  async getProxy(proxyId) {\n    const api = await createApiInstance();\n    const response = await api.get(`/proxies/${proxyId}`);\n    return response.data;\n  },\n  // Create new proxy\n  async createProxy(proxyData) {\n    const api = await createApiInstance();\n    const response = await api.post('/proxies/', proxyData);\n    return response.data;\n  },\n  // Update proxy\n  async updateProxy(proxyId, updates) {\n    const api = await createApiInstance();\n    const response = await api.put(`/proxies/${proxyId}`, updates);\n    return response.data;\n  },\n  // Delete proxy\n  async deleteProxy(proxyId) {\n    const api = await createApiInstance();\n    const response = await api.delete(`/proxies/${proxyId}`);\n    return response.data;\n  },\n  // Validate proxy\n  async validateProxy(proxyId) {\n    const api = await createApiInstance();\n    const response = await api.post(`/proxies/${proxyId}/validate`);\n    return response.data;\n  },\n  // Test proxy with URL\n  async testProxy(proxyId, testUrl = 'https://httpbin.org/ip') {\n    const api = await createApiInstance();\n    const response = await api.post(`/proxies/${proxyId}/test`, {\n      test_url: testUrl\n    });\n    return response.data;\n  },\n  // Get proxy statistics\n  async getStatistics() {\n    const api = await createApiInstance();\n    const response = await api.get('/proxies/statistics');\n    return response.data;\n  },\n  // Validate all proxies\n  async validateAll() {\n    const api = await createApiInstance();\n    const response = await api.post('/proxies/validate-all');\n    return response.data;\n  },\n  // Import proxies\n  async importProxies(proxies, validateOnImport = false) {\n    const api = await createApiInstance();\n    const response = await api.post('/proxies/import', {\n      proxies,\n      validate_on_import: validateOnImport\n    });\n    return response.data;\n  }\n};\n\n// TikTok Account API\nexport const accountAPI = {\n  // Get all accounts\n  async getAccounts(params = {}) {\n    const api = await createApiInstance();\n    const response = await api.get('/accounts/', {\n      params\n    });\n    return response.data;\n  },\n  // Get account by ID\n  async getAccount(accountId) {\n    const api = await createApiInstance();\n    const response = await api.get(`/accounts/${accountId}`);\n    return response.data;\n  },\n  // Create new account\n  async createAccount(accountData) {\n    const api = await createApiInstance();\n    const response = await api.post('/accounts/', accountData);\n    return response.data;\n  },\n  // Update account\n  async updateAccount(accountId, updates) {\n    const api = await createApiInstance();\n    const response = await api.put(`/accounts/${accountId}`, updates);\n    return response.data;\n  },\n  // Delete account\n  async deleteAccount(accountId) {\n    const api = await createApiInstance();\n    const response = await api.delete(`/accounts/${accountId}`);\n    return response.data;\n  },\n  // Login to TikTok\n  async login(accountId) {\n    const api = await createApiInstance();\n    const response = await api.post(`/accounts/${accountId}/login`);\n    return response.data;\n  },\n  // Complete login\n  async completeLogin(accountId) {\n    const api = await createApiInstance();\n    const response = await api.post(`/accounts/${accountId}/complete-login`);\n    return response.data;\n  },\n  // Check login status\n  async checkLoginStatus(accountId) {\n    const api = await createApiInstance();\n    const response = await api.get(`/accounts/${accountId}/login-status`);\n    return response.data;\n  }\n};\n\n// Task API\nexport const taskAPI = {\n  // Get all tasks\n  async getTasks(params = {}) {\n    const api = await createApiInstance();\n    const response = await api.get('/tasks/', {\n      params\n    });\n    return response.data;\n  },\n  // Get task by ID\n  async getTask(taskId) {\n    const api = await createApiInstance();\n    const response = await api.get(`/tasks/${taskId}`);\n    return response.data;\n  },\n  // Create new task\n  async createTask(taskData) {\n    const api = await createApiInstance();\n    const response = await api.post('/tasks/', taskData);\n    return response.data;\n  },\n  // Start task\n  async startTask(taskId) {\n    const api = await createApiInstance();\n    const response = await api.post(`/tasks/${taskId}/start`);\n    return response.data;\n  },\n  // Pause task\n  async pauseTask(taskId) {\n    const api = await createApiInstance();\n    const response = await api.post(`/tasks/${taskId}/pause`);\n    return response.data;\n  },\n  // Stop task\n  async stopTask(taskId) {\n    const api = await createApiInstance();\n    const response = await api.post(`/tasks/${taskId}/stop`);\n    return response.data;\n  },\n  // Delete task\n  async deleteTask(taskId) {\n    const api = await createApiInstance();\n    const response = await api.delete(`/tasks/${taskId}`);\n    return response.data;\n  }\n};\n\n// System API\nexport const systemAPI = {\n  // Get system status\n  async getStatus() {\n    const api = await createApiInstance();\n    const response = await api.get('/system/status');\n    return response.data;\n  },\n  // Get system logs\n  async getLogs(params = {}) {\n    const api = await createApiInstance();\n    const response = await api.get('/system/logs', {\n      params\n    });\n    return response.data;\n  },\n  // Clear logs\n  async clearLogs() {\n    const api = await createApiInstance();\n    const response = await api.delete('/system/logs');\n    return response.data;\n  },\n  // Export logs\n  async exportLogs(format = 'txt') {\n    const api = await createApiInstance();\n    const response = await api.get(`/system/logs/export?format=${format}`, {\n      responseType: 'blob'\n    });\n    return response.data;\n  }\n};\n\n// Error handler\nexport const handleApiError = error => {\n  if (error.response) {\n    // Server responded with error status\n    const {\n      status,\n      data\n    } = error.response;\n    return {\n      status,\n      message: data.detail || data.message || 'Server error',\n      data: data\n    };\n  } else if (error.request) {\n    // Request was made but no response received\n    return {\n      status: 0,\n      message: 'Network error - unable to connect to server',\n      data: null\n    };\n  } else {\n    // Something else happened\n    return {\n      status: -1,\n      message: error.message || 'Unknown error',\n      data: null\n    };\n  }\n};\nexport default {\n  profileAPI,\n  proxyAPI,\n  accountAPI,\n  taskAPI,\n  systemAPI,\n  handleApiError\n};", "map": {"version": 3, "names": ["axios", "getBackendUrl", "window", "electronAPI", "createApiInstance", "baseURL", "create", "timeout", "headers", "profileAPI", "getProfiles", "params", "api", "response", "get", "data", "getProfile", "profileId", "createProfile", "profileData", "post", "updateProfile", "updates", "put", "deleteProfile", "delete", "duplicateProfile", "newName", "regenerateFingerprint", "new_name", "regenerate_fingerprint", "testProfile", "getTemplates", "createFromTemplate", "templateName", "profileName", "proxyId", "profile_name", "proxy_id", "proxyAPI", "getProxies", "getProxy", "createProxy", "proxyData", "updateProxy", "deleteProxy", "validateProxy", "testProxy", "testUrl", "test_url", "getStatistics", "validateAll", "importProxies", "proxies", "validateOnImport", "validate_on_import", "accountAPI", "getAccounts", "getAccount", "accountId", "createAccount", "accountData", "updateAccount", "deleteAccount", "login", "completeLogin", "checkLoginStatus", "taskAPI", "getTasks", "getTask", "taskId", "createTask", "taskData", "startTask", "pauseTask", "stopTask", "deleteTask", "systemAPI", "getStatus", "getLogs", "clearLogs", "exportLogs", "format", "responseType", "handleApiError", "error", "status", "message", "detail", "request"], "sources": ["/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/services/api.js"], "sourcesContent": ["/**\n * API Service for TikTok Automation Backend\n */\n\nimport axios from 'axios';\n\n// Get backend URL\nconst getBackendUrl = async () => {\n  if (window.electronAPI) {\n    return await window.electronAPI.getBackendUrl();\n  }\n  return 'http://localhost:8000';\n};\n\n// Create axios instance\nconst createApiInstance = async () => {\n  const baseURL = await getBackendUrl();\n  return axios.create({\n    baseURL: `${baseURL}/api/v1`,\n    timeout: 30000,\n    headers: {\n      'Content-Type': 'application/json',\n    },\n  });\n};\n\n// Profile API\nexport const profileAPI = {\n  // Get all profiles\n  async getProfiles(params = {}) {\n    const api = await createApiInstance();\n    const response = await api.get('/profiles/', { params });\n    return response.data;\n  },\n\n  // Get profile by ID\n  async getProfile(profileId) {\n    const api = await createApiInstance();\n    const response = await api.get(`/profiles/${profileId}`);\n    return response.data;\n  },\n\n  // Create new profile\n  async createProfile(profileData) {\n    const api = await createApiInstance();\n    const response = await api.post('/profiles/', profileData);\n    return response.data;\n  },\n\n  // Update profile\n  async updateProfile(profileId, updates) {\n    const api = await createApiInstance();\n    const response = await api.put(`/profiles/${profileId}`, updates);\n    return response.data;\n  },\n\n  // Delete profile\n  async deleteProfile(profileId) {\n    const api = await createApiInstance();\n    const response = await api.delete(`/profiles/${profileId}`);\n    return response.data;\n  },\n\n  // Duplicate profile\n  async duplicateProfile(profileId, newName, regenerateFingerprint = false) {\n    const api = await createApiInstance();\n    const response = await api.post(`/profiles/${profileId}/duplicate`, {\n      new_name: newName,\n      regenerate_fingerprint: regenerateFingerprint\n    });\n    return response.data;\n  },\n\n  // Test profile\n  async testProfile(profileId) {\n    const api = await createApiInstance();\n    const response = await api.post(`/profiles/${profileId}/test`);\n    return response.data;\n  },\n\n  // Get profile templates\n  async getTemplates() {\n    const api = await createApiInstance();\n    const response = await api.get('/profiles/templates/list');\n    return response.data;\n  },\n\n  // Create from template\n  async createFromTemplate(templateName, profileName, proxyId = null) {\n    const api = await createApiInstance();\n    const response = await api.post(`/profiles/templates/${templateName}`, {\n      profile_name: profileName,\n      proxy_id: proxyId\n    });\n    return response.data;\n  }\n};\n\n// Proxy API\nexport const proxyAPI = {\n  // Get all proxies\n  async getProxies(params = {}) {\n    const api = await createApiInstance();\n    const response = await api.get('/proxies/', { params });\n    return response.data;\n  },\n\n  // Get proxy by ID\n  async getProxy(proxyId) {\n    const api = await createApiInstance();\n    const response = await api.get(`/proxies/${proxyId}`);\n    return response.data;\n  },\n\n  // Create new proxy\n  async createProxy(proxyData) {\n    const api = await createApiInstance();\n    const response = await api.post('/proxies/', proxyData);\n    return response.data;\n  },\n\n  // Update proxy\n  async updateProxy(proxyId, updates) {\n    const api = await createApiInstance();\n    const response = await api.put(`/proxies/${proxyId}`, updates);\n    return response.data;\n  },\n\n  // Delete proxy\n  async deleteProxy(proxyId) {\n    const api = await createApiInstance();\n    const response = await api.delete(`/proxies/${proxyId}`);\n    return response.data;\n  },\n\n  // Validate proxy\n  async validateProxy(proxyId) {\n    const api = await createApiInstance();\n    const response = await api.post(`/proxies/${proxyId}/validate`);\n    return response.data;\n  },\n\n  // Test proxy with URL\n  async testProxy(proxyId, testUrl = 'https://httpbin.org/ip') {\n    const api = await createApiInstance();\n    const response = await api.post(`/proxies/${proxyId}/test`, { test_url: testUrl });\n    return response.data;\n  },\n\n  // Get proxy statistics\n  async getStatistics() {\n    const api = await createApiInstance();\n    const response = await api.get('/proxies/statistics');\n    return response.data;\n  },\n\n  // Validate all proxies\n  async validateAll() {\n    const api = await createApiInstance();\n    const response = await api.post('/proxies/validate-all');\n    return response.data;\n  },\n\n  // Import proxies\n  async importProxies(proxies, validateOnImport = false) {\n    const api = await createApiInstance();\n    const response = await api.post('/proxies/import', {\n      proxies,\n      validate_on_import: validateOnImport\n    });\n    return response.data;\n  }\n};\n\n// TikTok Account API\nexport const accountAPI = {\n  // Get all accounts\n  async getAccounts(params = {}) {\n    const api = await createApiInstance();\n    const response = await api.get('/accounts/', { params });\n    return response.data;\n  },\n\n  // Get account by ID\n  async getAccount(accountId) {\n    const api = await createApiInstance();\n    const response = await api.get(`/accounts/${accountId}`);\n    return response.data;\n  },\n\n  // Create new account\n  async createAccount(accountData) {\n    const api = await createApiInstance();\n    const response = await api.post('/accounts/', accountData);\n    return response.data;\n  },\n\n  // Update account\n  async updateAccount(accountId, updates) {\n    const api = await createApiInstance();\n    const response = await api.put(`/accounts/${accountId}`, updates);\n    return response.data;\n  },\n\n  // Delete account\n  async deleteAccount(accountId) {\n    const api = await createApiInstance();\n    const response = await api.delete(`/accounts/${accountId}`);\n    return response.data;\n  },\n\n  // Login to TikTok\n  async login(accountId) {\n    const api = await createApiInstance();\n    const response = await api.post(`/accounts/${accountId}/login`);\n    return response.data;\n  },\n\n  // Complete login\n  async completeLogin(accountId) {\n    const api = await createApiInstance();\n    const response = await api.post(`/accounts/${accountId}/complete-login`);\n    return response.data;\n  },\n\n  // Check login status\n  async checkLoginStatus(accountId) {\n    const api = await createApiInstance();\n    const response = await api.get(`/accounts/${accountId}/login-status`);\n    return response.data;\n  }\n};\n\n// Task API\nexport const taskAPI = {\n  // Get all tasks\n  async getTasks(params = {}) {\n    const api = await createApiInstance();\n    const response = await api.get('/tasks/', { params });\n    return response.data;\n  },\n\n  // Get task by ID\n  async getTask(taskId) {\n    const api = await createApiInstance();\n    const response = await api.get(`/tasks/${taskId}`);\n    return response.data;\n  },\n\n  // Create new task\n  async createTask(taskData) {\n    const api = await createApiInstance();\n    const response = await api.post('/tasks/', taskData);\n    return response.data;\n  },\n\n  // Start task\n  async startTask(taskId) {\n    const api = await createApiInstance();\n    const response = await api.post(`/tasks/${taskId}/start`);\n    return response.data;\n  },\n\n  // Pause task\n  async pauseTask(taskId) {\n    const api = await createApiInstance();\n    const response = await api.post(`/tasks/${taskId}/pause`);\n    return response.data;\n  },\n\n  // Stop task\n  async stopTask(taskId) {\n    const api = await createApiInstance();\n    const response = await api.post(`/tasks/${taskId}/stop`);\n    return response.data;\n  },\n\n  // Delete task\n  async deleteTask(taskId) {\n    const api = await createApiInstance();\n    const response = await api.delete(`/tasks/${taskId}`);\n    return response.data;\n  }\n};\n\n// System API\nexport const systemAPI = {\n  // Get system status\n  async getStatus() {\n    const api = await createApiInstance();\n    const response = await api.get('/system/status');\n    return response.data;\n  },\n\n  // Get system logs\n  async getLogs(params = {}) {\n    const api = await createApiInstance();\n    const response = await api.get('/system/logs', { params });\n    return response.data;\n  },\n\n  // Clear logs\n  async clearLogs() {\n    const api = await createApiInstance();\n    const response = await api.delete('/system/logs');\n    return response.data;\n  },\n\n  // Export logs\n  async exportLogs(format = 'txt') {\n    const api = await createApiInstance();\n    const response = await api.get(`/system/logs/export?format=${format}`, {\n      responseType: 'blob'\n    });\n    return response.data;\n  }\n};\n\n// Error handler\nexport const handleApiError = (error) => {\n  if (error.response) {\n    // Server responded with error status\n    const { status, data } = error.response;\n    return {\n      status,\n      message: data.detail || data.message || 'Server error',\n      data: data\n    };\n  } else if (error.request) {\n    // Request was made but no response received\n    return {\n      status: 0,\n      message: 'Network error - unable to connect to server',\n      data: null\n    };\n  } else {\n    // Something else happened\n    return {\n      status: -1,\n      message: error.message || 'Unknown error',\n      data: null\n    };\n  }\n};\n\nexport default {\n  profileAPI,\n  proxyAPI,\n  accountAPI,\n  taskAPI,\n  systemAPI,\n  handleApiError\n};\n"], "mappings": "AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;EAChC,IAAIC,MAAM,CAACC,WAAW,EAAE;IACtB,OAAO,MAAMD,MAAM,CAACC,WAAW,CAACF,aAAa,CAAC,CAAC;EACjD;EACA,OAAO,uBAAuB;AAChC,CAAC;;AAED;AACA,MAAMG,iBAAiB,GAAG,MAAAA,CAAA,KAAY;EACpC,MAAMC,OAAO,GAAG,MAAMJ,aAAa,CAAC,CAAC;EACrC,OAAOD,KAAK,CAACM,MAAM,CAAC;IAClBD,OAAO,EAAE,GAAGA,OAAO,SAAS;IAC5BE,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE;MACP,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAO,MAAMC,UAAU,GAAG;EACxB;EACA,MAAMC,WAAWA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;IAC7B,MAAMC,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACE,GAAG,CAAC,YAAY,EAAE;MAAEH;IAAO,CAAC,CAAC;IACxD,OAAOE,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMC,UAAUA,CAACC,SAAS,EAAE;IAC1B,MAAML,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACE,GAAG,CAAC,aAAaG,SAAS,EAAE,CAAC;IACxD,OAAOJ,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMG,aAAaA,CAACC,WAAW,EAAE;IAC/B,MAAMP,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACQ,IAAI,CAAC,YAAY,EAAED,WAAW,CAAC;IAC1D,OAAON,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMM,aAAaA,CAACJ,SAAS,EAAEK,OAAO,EAAE;IACtC,MAAMV,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACW,GAAG,CAAC,aAAaN,SAAS,EAAE,EAAEK,OAAO,CAAC;IACjE,OAAOT,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMS,aAAaA,CAACP,SAAS,EAAE;IAC7B,MAAML,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACa,MAAM,CAAC,aAAaR,SAAS,EAAE,CAAC;IAC3D,OAAOJ,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMW,gBAAgBA,CAACT,SAAS,EAAEU,OAAO,EAAEC,qBAAqB,GAAG,KAAK,EAAE;IACxE,MAAMhB,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACQ,IAAI,CAAC,aAAaH,SAAS,YAAY,EAAE;MAClEY,QAAQ,EAAEF,OAAO;MACjBG,sBAAsB,EAAEF;IAC1B,CAAC,CAAC;IACF,OAAOf,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMgB,WAAWA,CAACd,SAAS,EAAE;IAC3B,MAAML,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACQ,IAAI,CAAC,aAAaH,SAAS,OAAO,CAAC;IAC9D,OAAOJ,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMiB,YAAYA,CAAA,EAAG;IACnB,MAAMpB,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACE,GAAG,CAAC,0BAA0B,CAAC;IAC1D,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMkB,kBAAkBA,CAACC,YAAY,EAAEC,WAAW,EAAEC,OAAO,GAAG,IAAI,EAAE;IAClE,MAAMxB,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACQ,IAAI,CAAC,uBAAuBc,YAAY,EAAE,EAAE;MACrEG,YAAY,EAAEF,WAAW;MACzBG,QAAQ,EAAEF;IACZ,CAAC,CAAC;IACF,OAAOvB,QAAQ,CAACE,IAAI;EACtB;AACF,CAAC;;AAED;AACA,OAAO,MAAMwB,QAAQ,GAAG;EACtB;EACA,MAAMC,UAAUA,CAAC7B,MAAM,GAAG,CAAC,CAAC,EAAE;IAC5B,MAAMC,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACE,GAAG,CAAC,WAAW,EAAE;MAAEH;IAAO,CAAC,CAAC;IACvD,OAAOE,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAM0B,QAAQA,CAACL,OAAO,EAAE;IACtB,MAAMxB,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACE,GAAG,CAAC,YAAYsB,OAAO,EAAE,CAAC;IACrD,OAAOvB,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAM2B,WAAWA,CAACC,SAAS,EAAE;IAC3B,MAAM/B,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACQ,IAAI,CAAC,WAAW,EAAEuB,SAAS,CAAC;IACvD,OAAO9B,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAM6B,WAAWA,CAACR,OAAO,EAAEd,OAAO,EAAE;IAClC,MAAMV,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACW,GAAG,CAAC,YAAYa,OAAO,EAAE,EAAEd,OAAO,CAAC;IAC9D,OAAOT,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAM8B,WAAWA,CAACT,OAAO,EAAE;IACzB,MAAMxB,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACa,MAAM,CAAC,YAAYW,OAAO,EAAE,CAAC;IACxD,OAAOvB,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAM+B,aAAaA,CAACV,OAAO,EAAE;IAC3B,MAAMxB,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACQ,IAAI,CAAC,YAAYgB,OAAO,WAAW,CAAC;IAC/D,OAAOvB,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMgC,SAASA,CAACX,OAAO,EAAEY,OAAO,GAAG,wBAAwB,EAAE;IAC3D,MAAMpC,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACQ,IAAI,CAAC,YAAYgB,OAAO,OAAO,EAAE;MAAEa,QAAQ,EAAED;IAAQ,CAAC,CAAC;IAClF,OAAOnC,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMmC,aAAaA,CAAA,EAAG;IACpB,MAAMtC,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACE,GAAG,CAAC,qBAAqB,CAAC;IACrD,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMoC,WAAWA,CAAA,EAAG;IAClB,MAAMvC,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACQ,IAAI,CAAC,uBAAuB,CAAC;IACxD,OAAOP,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMqC,aAAaA,CAACC,OAAO,EAAEC,gBAAgB,GAAG,KAAK,EAAE;IACrD,MAAM1C,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACQ,IAAI,CAAC,iBAAiB,EAAE;MACjDiC,OAAO;MACPE,kBAAkB,EAAED;IACtB,CAAC,CAAC;IACF,OAAOzC,QAAQ,CAACE,IAAI;EACtB;AACF,CAAC;;AAED;AACA,OAAO,MAAMyC,UAAU,GAAG;EACxB;EACA,MAAMC,WAAWA,CAAC9C,MAAM,GAAG,CAAC,CAAC,EAAE;IAC7B,MAAMC,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACE,GAAG,CAAC,YAAY,EAAE;MAAEH;IAAO,CAAC,CAAC;IACxD,OAAOE,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAM2C,UAAUA,CAACC,SAAS,EAAE;IAC1B,MAAM/C,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACE,GAAG,CAAC,aAAa6C,SAAS,EAAE,CAAC;IACxD,OAAO9C,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAM6C,aAAaA,CAACC,WAAW,EAAE;IAC/B,MAAMjD,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACQ,IAAI,CAAC,YAAY,EAAEyC,WAAW,CAAC;IAC1D,OAAOhD,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAM+C,aAAaA,CAACH,SAAS,EAAErC,OAAO,EAAE;IACtC,MAAMV,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACW,GAAG,CAAC,aAAaoC,SAAS,EAAE,EAAErC,OAAO,CAAC;IACjE,OAAOT,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMgD,aAAaA,CAACJ,SAAS,EAAE;IAC7B,MAAM/C,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACa,MAAM,CAAC,aAAakC,SAAS,EAAE,CAAC;IAC3D,OAAO9C,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMiD,KAAKA,CAACL,SAAS,EAAE;IACrB,MAAM/C,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACQ,IAAI,CAAC,aAAauC,SAAS,QAAQ,CAAC;IAC/D,OAAO9C,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMkD,aAAaA,CAACN,SAAS,EAAE;IAC7B,MAAM/C,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACQ,IAAI,CAAC,aAAauC,SAAS,iBAAiB,CAAC;IACxE,OAAO9C,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMmD,gBAAgBA,CAACP,SAAS,EAAE;IAChC,MAAM/C,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACE,GAAG,CAAC,aAAa6C,SAAS,eAAe,CAAC;IACrE,OAAO9C,QAAQ,CAACE,IAAI;EACtB;AACF,CAAC;;AAED;AACA,OAAO,MAAMoD,OAAO,GAAG;EACrB;EACA,MAAMC,QAAQA,CAACzD,MAAM,GAAG,CAAC,CAAC,EAAE;IAC1B,MAAMC,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACE,GAAG,CAAC,SAAS,EAAE;MAAEH;IAAO,CAAC,CAAC;IACrD,OAAOE,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMsD,OAAOA,CAACC,MAAM,EAAE;IACpB,MAAM1D,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACE,GAAG,CAAC,UAAUwD,MAAM,EAAE,CAAC;IAClD,OAAOzD,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMwD,UAAUA,CAACC,QAAQ,EAAE;IACzB,MAAM5D,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACQ,IAAI,CAAC,SAAS,EAAEoD,QAAQ,CAAC;IACpD,OAAO3D,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAM0D,SAASA,CAACH,MAAM,EAAE;IACtB,MAAM1D,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACQ,IAAI,CAAC,UAAUkD,MAAM,QAAQ,CAAC;IACzD,OAAOzD,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAM2D,SAASA,CAACJ,MAAM,EAAE;IACtB,MAAM1D,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACQ,IAAI,CAAC,UAAUkD,MAAM,QAAQ,CAAC;IACzD,OAAOzD,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAM4D,QAAQA,CAACL,MAAM,EAAE;IACrB,MAAM1D,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACQ,IAAI,CAAC,UAAUkD,MAAM,OAAO,CAAC;IACxD,OAAOzD,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAM6D,UAAUA,CAACN,MAAM,EAAE;IACvB,MAAM1D,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACa,MAAM,CAAC,UAAU6C,MAAM,EAAE,CAAC;IACrD,OAAOzD,QAAQ,CAACE,IAAI;EACtB;AACF,CAAC;;AAED;AACA,OAAO,MAAM8D,SAAS,GAAG;EACvB;EACA,MAAMC,SAASA,CAAA,EAAG;IAChB,MAAMlE,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACE,GAAG,CAAC,gBAAgB,CAAC;IAChD,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMgE,OAAOA,CAACpE,MAAM,GAAG,CAAC,CAAC,EAAE;IACzB,MAAMC,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACE,GAAG,CAAC,cAAc,EAAE;MAAEH;IAAO,CAAC,CAAC;IAC1D,OAAOE,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMiE,SAASA,CAAA,EAAG;IAChB,MAAMpE,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACa,MAAM,CAAC,cAAc,CAAC;IACjD,OAAOZ,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACA,MAAMkE,UAAUA,CAACC,MAAM,GAAG,KAAK,EAAE;IAC/B,MAAMtE,GAAG,GAAG,MAAMR,iBAAiB,CAAC,CAAC;IACrC,MAAMS,QAAQ,GAAG,MAAMD,GAAG,CAACE,GAAG,CAAC,8BAA8BoE,MAAM,EAAE,EAAE;MACrEC,YAAY,EAAE;IAChB,CAAC,CAAC;IACF,OAAOtE,QAAQ,CAACE,IAAI;EACtB;AACF,CAAC;;AAED;AACA,OAAO,MAAMqE,cAAc,GAAIC,KAAK,IAAK;EACvC,IAAIA,KAAK,CAACxE,QAAQ,EAAE;IAClB;IACA,MAAM;MAAEyE,MAAM;MAAEvE;IAAK,CAAC,GAAGsE,KAAK,CAACxE,QAAQ;IACvC,OAAO;MACLyE,MAAM;MACNC,OAAO,EAAExE,IAAI,CAACyE,MAAM,IAAIzE,IAAI,CAACwE,OAAO,IAAI,cAAc;MACtDxE,IAAI,EAAEA;IACR,CAAC;EACH,CAAC,MAAM,IAAIsE,KAAK,CAACI,OAAO,EAAE;IACxB;IACA,OAAO;MACLH,MAAM,EAAE,CAAC;MACTC,OAAO,EAAE,6CAA6C;MACtDxE,IAAI,EAAE;IACR,CAAC;EACH,CAAC,MAAM;IACL;IACA,OAAO;MACLuE,MAAM,EAAE,CAAC,CAAC;MACVC,OAAO,EAAEF,KAAK,CAACE,OAAO,IAAI,eAAe;MACzCxE,IAAI,EAAE;IACR,CAAC;EACH;AACF,CAAC;AAED,eAAe;EACbN,UAAU;EACV8B,QAAQ;EACRiB,UAAU;EACVW,OAAO;EACPU,SAAS;EACTO;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}