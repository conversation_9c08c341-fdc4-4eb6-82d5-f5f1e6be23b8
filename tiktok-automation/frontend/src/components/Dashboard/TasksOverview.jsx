/**
 * Tasks Overview Component for Dashboard
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FiPlay,
  FiPause,
  FiClock,
  FiCheckCircle,
  FiAlertCircle,
  FiMoreHorizontal
} from 'react-icons/fi';

const TasksOverview = () => {
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);

  // Fetch tasks data
  useEffect(() => {
    const fetchTasks = async () => {
      try {
        let baseUrl = 'http://localhost:8000';
        if (window.electronAPI) {
          baseUrl = await window.electronAPI.getBackendUrl();
        }

        const response = await fetch(`${baseUrl}/api/v1/tasks/?active_only=true&limit=5`);
        const data = await response.json();
        setTasks(data);
      } catch (error) {
        console.error('Failed to fetch tasks:', error);
        // Mock data for development
        setTasks([
          {
            id: 1,
            name: 'Follow @competitor1 followers',
            status: 'running',
            progress_percentage: 65,
            total_processed: 130,
            target_count: 200,
            task_type: 'follow_followers'
          },
          {
            id: 2,
            name: 'Unfollow inactive accounts',
            status: 'paused',
            progress_percentage: 30,
            total_processed: 45,
            target_count: 150,
            task_type: 'unfollow_users'
          },
          {
            id: 3,
            name: 'Follow @competitor2 followers',
            status: 'pending',
            progress_percentage: 0,
            total_processed: 0,
            target_count: 100,
            task_type: 'follow_followers'
          }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchTasks();
    const interval = setInterval(fetchTasks, 10000); // Refresh every 10 seconds
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = (status) => {
    switch (status) {
      case 'running':
        return <FiPlay className="w-4 h-4 text-green-600" />;
      case 'paused':
        return <FiPause className="w-4 h-4 text-yellow-600" />;
      case 'completed':
        return <FiCheckCircle className="w-4 h-4 text-blue-600" />;
      case 'failed':
        return <FiAlertCircle className="w-4 h-4 text-red-600" />;
      default:
        return <FiClock className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'running':
        return 'bg-green-100 text-green-800';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTaskTypeLabel = (taskType) => {
    switch (taskType) {
      case 'follow_followers':
        return 'Follow Followers';
      case 'follow_following':
        return 'Follow Following';
      case 'unfollow_users':
        return 'Unfollow Users';
      default:
        return 'Unknown';
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Active Tasks</h2>
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-2 bg-gray-200 rounded w-full mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold text-gray-900">Active Tasks</h2>
        <button
          onClick={() => window.location.href = '/tasks'}
          className="text-sm text-blue-600 hover:text-blue-800 font-medium"
        >
          View All
        </button>
      </div>

      {tasks.length === 0 ? (
        <div className="text-center py-8">
          <FiPlay className="w-12 h-12 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Tasks</h3>
          <p className="text-gray-600 mb-4">Create your first automation task to get started.</p>
          <button
            onClick={() => window.location.href = '/tasks/new'}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Create Task
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          {tasks.map((task, index) => (
            <motion.div
              key={task.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors"
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(task.status)}
                  <div>
                    <h3 className="font-medium text-gray-900">{task.name}</h3>
                    <p className="text-sm text-gray-600">
                      {getTaskTypeLabel(task.task_type)}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(task.status)}`}>
                    {task.status.charAt(0).toUpperCase() + task.status.slice(1)}
                  </span>
                  <button className="p-1 text-gray-400 hover:text-gray-600">
                    <FiMoreHorizontal className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {/* Progress Bar */}
              <div className="mb-3">
                <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
                  <span>Progress</span>
                  <span>{task.progress_percentage}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <motion.div
                    className="bg-blue-600 h-2 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${task.progress_percentage}%` }}
                    transition={{ duration: 0.5 }}
                  />
                </div>
              </div>

              {/* Stats */}
              <div className="flex items-center justify-between text-sm text-gray-600">
                <span>
                  {task.total_processed} / {task.target_count} completed
                </span>
                {task.status === 'running' && (
                  <span className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
                    Running
                  </span>
                )}
              </div>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
};

export default TasksOverview;
