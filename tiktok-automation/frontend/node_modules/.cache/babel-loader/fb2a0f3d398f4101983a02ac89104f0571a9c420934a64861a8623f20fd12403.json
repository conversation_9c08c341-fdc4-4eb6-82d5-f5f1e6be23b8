{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/pages/Dashboard.jsx\",\n  _s = $RefreshSig$();\n/**\n * TikTok Automation Dashboard - Main management interface\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { useOutletContext } from 'react-router-dom';\nimport { FiPlus, FiPlay, FiStopCircle, FiUsers, FiActivity, FiTarget, FiTrendingUp } from 'react-icons/fi';\n\n// Components\nimport StatsCard from '../components/Dashboard/StatsCard';\nimport ProfileTable from '../components/Dashboard/ProfileTable';\nimport ProfileForm from '../components/Dashboard/ProfileForm';\nimport LogMonitor from '../components/Dashboard/LogMonitor';\nimport SystemStatus from '../components/Dashboard/SystemStatus';\nimport TasksOverview from '../components/Dashboard/TasksOverview';\nimport RecentActivity from '../components/Dashboard/RecentActivity';\n\n// Services\nimport profileService from '../services/profileService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const {\n    setPageTitle,\n    setPageSubtitle\n  } = useOutletContext();\n\n  // State management\n  const [profiles, setProfiles] = useState([]);\n  const [showProfileForm, setShowProfileForm] = useState(false);\n  const [selectedProfiles, setSelectedProfiles] = useState([]);\n  const [logs, setLogs] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Set page title and subtitle\n  useEffect(() => {\n    setPageTitle('Dashboard');\n    setPageSubtitle('Welcome to TikTok Automation');\n  }, [setPageTitle, setPageSubtitle]);\n\n  // Load data from backend\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        setLoading(true);\n\n        // Load profiles from backend\n        await profileService.loadProfiles();\n        setProfiles(profileService.getProfiles());\n\n        // Mock logs for now - TODO: Load from backend\n        const mockLogs = [{\n          id: 1,\n          time: '09:45:30',\n          level: 'INFO',\n          message: 'System started successfully'\n        }, {\n          id: 2,\n          time: '09:45:31',\n          level: 'INFO',\n          message: 'Backend connection established'\n        }, {\n          id: 3,\n          time: '09:45:32',\n          level: 'INFO',\n          message: 'Profile service initialized'\n        }, {\n          id: 4,\n          time: '09:45:33',\n          level: 'INFO',\n          message: 'Ready for automation tasks'\n        }];\n        setLogs(mockLogs);\n      } catch (error) {\n        console.error('Failed to load data:', error);\n        // Add error log\n        setLogs(prev => [...prev, {\n          id: Date.now(),\n          time: new Date().toLocaleTimeString(),\n          level: 'ERROR',\n          message: `Failed to load profiles: ${error.message}`\n        }]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadData();\n\n    // Set up profile service listeners\n    const handleProfileEvent = (event, data) => {\n      const timestamp = new Date().toLocaleTimeString();\n      let logMessage = '';\n      switch (event) {\n        case 'profiles_loaded':\n          setProfiles(data);\n          logMessage = `Loaded ${data.length} profiles from backend`;\n          break;\n        case 'profile_created':\n          setProfiles(profileService.getProfiles());\n          logMessage = `Profile \"${data.username}\" created successfully`;\n          break;\n        case 'profile_updated':\n          setProfiles(profileService.getProfiles());\n          logMessage = `Profile \"${data.username}\" updated`;\n          break;\n        case 'profile_deleted':\n          setProfiles(profileService.getProfiles());\n          logMessage = `Profile deleted (ID: ${data})`;\n          break;\n        case 'login_started':\n          logMessage = `Login started for profile ID: ${data}`;\n          break;\n        case 'login_completed':\n          logMessage = `Login completed for profile ID: ${data}`;\n          break;\n        case 'automation_started':\n          logMessage = `Automation started for profile ID: ${data}`;\n          break;\n        case 'automation_paused':\n          logMessage = `Automation paused for profile ID: ${data}`;\n          break;\n        case 'automation_stopped':\n          logMessage = `Automation stopped for profile ID: ${data}`;\n          break;\n        default:\n          return;\n      }\n      if (logMessage) {\n        setLogs(prev => [...prev, {\n          id: Date.now(),\n          time: timestamp,\n          level: 'INFO',\n          message: logMessage\n        }]);\n      }\n    };\n    profileService.addListener(handleProfileEvent);\n\n    // Cleanup\n    return () => {\n      profileService.removeListener(handleProfileEvent);\n    };\n  }, []);\n\n  // Handle profile actions\n  const handleProfileAction = async (profileId, action) => {\n    try {\n      let result;\n      switch (action) {\n        case 'login':\n          result = await profileService.startLogin(profileId);\n          break;\n        case 'complete':\n          result = await profileService.completeLogin(profileId);\n          break;\n        case 'start':\n          result = await profileService.startAutomation(profileId);\n          break;\n        case 'pause':\n          result = await profileService.pauseAutomation(profileId);\n          break;\n        case 'stop':\n          result = await profileService.stopAutomation(profileId);\n          break;\n        default:\n          throw new Error(`Unknown action: ${action}`);\n      }\n      console.log(`Action ${action} for profile ${profileId}:`, result);\n    } catch (error) {\n      console.error(`Failed to execute action ${action} for profile ${profileId}:`, error);\n\n      // Add error log\n      setLogs(prev => [...prev, {\n        id: Date.now(),\n        time: new Date().toLocaleTimeString(),\n        level: 'ERROR',\n        message: `Failed to ${action} profile ${profileId}: ${error.message}`\n      }]);\n    }\n  };\n  const handleProfileSelect = (profileId, isSelected) => {\n    setSelectedProfiles(prev => {\n      if (isSelected) {\n        return [...prev, profileId];\n      } else {\n        return prev.filter(id => id !== profileId);\n      }\n    });\n  };\n  const handleSelectAll = isSelected => {\n    if (isSelected) {\n      setSelectedProfiles(profiles.map(p => p.id));\n    } else {\n      setSelectedProfiles([]);\n    }\n  };\n  const handleBulkAction = async action => {\n    if (selectedProfiles.length === 0) {\n      return;\n    }\n    try {\n      console.log(`Bulk action ${action} for profiles:`, selectedProfiles);\n      const results = await profileService.bulkAction(selectedProfiles, action);\n\n      // Log results\n      const successCount = results.filter(r => r.success).length;\n      const failCount = results.filter(r => !r.success).length;\n      setLogs(prev => [...prev, {\n        id: Date.now(),\n        time: new Date().toLocaleTimeString(),\n        level: 'INFO',\n        message: `Bulk ${action}: ${successCount} successful, ${failCount} failed`\n      }]);\n    } catch (error) {\n      console.error(`Failed to execute bulk action ${action}:`, error);\n      setLogs(prev => [...prev, {\n        id: Date.now(),\n        time: new Date().toLocaleTimeString(),\n        level: 'ERROR',\n        message: `Bulk ${action} failed: ${error.message}`\n      }]);\n    }\n  };\n  const handleCreateProfile = async profileData => {\n    try {\n      console.log('Creating profile:', profileData);\n\n      // Create profile using service\n      await profileService.createProfile(profileData);\n      setShowProfileForm(false);\n    } catch (error) {\n      console.error('Failed to create profile:', error);\n\n      // Add error log\n      setLogs(prev => [...prev, {\n        id: Date.now(),\n        time: new Date().toLocaleTimeString(),\n        level: 'ERROR',\n        message: `Failed to create profile \"${profileData.profileName}\": ${error.message}`\n      }]);\n\n      // Show error to user (you might want to add a toast notification here)\n      alert(`Failed to create profile: ${error.message}`);\n    }\n  };\n  const handleClearLogs = () => {\n    setLogs([]);\n  };\n  const handleExportLogs = () => {\n    console.log('Exporting logs...');\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Calculate stats\n  const totalProfiles = profiles.length;\n  const activeProfiles = profiles.filter(p => p.status === 'running').length;\n  const totalFollows = profiles.reduce((sum, p) => sum + (p.followsToday || 0), 0);\n  const successRate = totalProfiles > 0 ? Math.round(activeProfiles / totalProfiles * 100) : 0;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(StatsCard, {\n        title: \"Total Profiles\",\n        value: totalProfiles,\n        icon: FiUsers,\n        color: \"blue\",\n        trend: \"+12%\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n        title: \"Active Tasks\",\n        value: activeProfiles,\n        icon: FiActivity,\n        color: \"green\",\n        trend: \"+5%\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n        title: \"Follows Today\",\n        value: totalFollows,\n        icon: FiTarget,\n        color: \"purple\",\n        trend: \"+23%\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatsCard, {\n        title: \"Success Rate\",\n        value: `${successRate}%`,\n        icon: FiTrendingUp,\n        color: \"orange\",\n        trend: \"+8%\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"lg:col-span-2 space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 border-b border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-lg font-semibold text-gray-900\",\n                children: [\"Profile Management (\", totalProfiles, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setShowProfileForm(true),\n                  className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(FiPlus, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"New Profile\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleBulkAction('start'),\n                  disabled: selectedProfiles.length === 0,\n                  className: \"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                  children: [/*#__PURE__*/_jsxDEV(FiPlay, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Start All\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 333,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleBulkAction('stop'),\n                  disabled: selectedProfiles.length === 0,\n                  className: \"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed\",\n                  children: [/*#__PURE__*/_jsxDEV(FiStopCircle, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Stop All\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 341,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProfileTable, {\n            profiles: profiles,\n            selectedProfiles: selectedProfiles,\n            onProfileSelect: handleProfileSelect,\n            onProfileAction: handleProfileAction,\n            onSelectAll: handleSelectAll\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TasksOverview, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(SystemStatus, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(RecentActivity, {\n          activities: logs.slice(-5)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LogMonitor, {\n          logs: logs,\n          onClearLogs: handleClearLogs,\n          onExportLogs: handleExportLogs\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ProfileForm, {\n      isOpen: showProfileForm,\n      onClose: () => setShowProfileForm(false),\n      onSubmit: handleCreateProfile\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 379,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 275,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"nR+nRMEJAvBuyJ+4/ngw8m+s/po=\", false, function () {\n  return [useOutletContext];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useOutletContext", "FiPlus", "FiPlay", "FiStopCircle", "FiUsers", "FiActivity", "<PERSON><PERSON><PERSON><PERSON>", "FiTrendingUp", "StatsCard", "ProfileTable", "ProfileForm", "LogMonitor", "SystemStatus", "TasksOverview", "RecentActivity", "profileService", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "setPageTitle", "setPageSubtitle", "profiles", "setProfiles", "showProfileForm", "setShowProfileForm", "selected<PERSON><PERSON><PERSON><PERSON>", "setSelectedProfiles", "logs", "setLogs", "loading", "setLoading", "loadData", "loadProfiles", "getProfiles", "mockLogs", "id", "time", "level", "message", "error", "console", "prev", "Date", "now", "toLocaleTimeString", "handleProfileEvent", "event", "data", "timestamp", "logMessage", "length", "username", "addListener", "removeListener", "handleProfileAction", "profileId", "action", "result", "startLogin", "completeLogin", "startAutomation", "pauseAutomation", "stopAutomation", "Error", "log", "handleProfileSelect", "isSelected", "filter", "handleSelectAll", "map", "p", "handleBulkAction", "results", "bulkAction", "successCount", "r", "success", "failCount", "handleCreateProfile", "profileData", "createProfile", "profileName", "alert", "handleClearLogs", "handleExportLogs", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "totalProfiles", "activeProfiles", "status", "totalFollows", "reduce", "sum", "<PERSON><PERSON><PERSON><PERSON>", "successRate", "Math", "round", "title", "value", "icon", "color", "trend", "onClick", "disabled", "onProfileSelect", "onProfileAction", "onSelectAll", "activities", "slice", "onClearLogs", "onExportLogs", "isOpen", "onClose", "onSubmit", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/pages/Dashboard.jsx"], "sourcesContent": ["/**\n * TikTok Automation Dashboard - Main management interface\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { useOutletContext } from 'react-router-dom';\nimport {\n  FiPlus,\n  FiPlay,\n  FiStopCircle,\n  FiUsers,\n  FiActivity,\n  FiTarget,\n  FiTrendingUp\n} from 'react-icons/fi';\n\n// Components\nimport StatsCard from '../components/Dashboard/StatsCard';\nimport ProfileTable from '../components/Dashboard/ProfileTable';\nimport ProfileForm from '../components/Dashboard/ProfileForm';\nimport LogMonitor from '../components/Dashboard/LogMonitor';\nimport SystemStatus from '../components/Dashboard/SystemStatus';\nimport TasksOverview from '../components/Dashboard/TasksOverview';\nimport RecentActivity from '../components/Dashboard/RecentActivity';\n\n// Services\nimport profileService from '../services/profileService';\n\nconst Dashboard = () => {\n  const { setPageTitle, setPageSubtitle } = useOutletContext();\n\n  // State management\n  const [profiles, setProfiles] = useState([]);\n  const [showProfileForm, setShowProfileForm] = useState(false);\n  const [selectedProfiles, setSelectedProfiles] = useState([]);\n\n  const [logs, setLogs] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Set page title and subtitle\n  useEffect(() => {\n    setPageTitle('Dashboard');\n    setPageSubtitle('Welcome to TikTok Automation');\n  }, [setPageTitle, setPageSubtitle]);\n\n  // Load data from backend\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        setLoading(true);\n\n        // Load profiles from backend\n        await profileService.loadProfiles();\n        setProfiles(profileService.getProfiles());\n\n        // Mock logs for now - TODO: Load from backend\n        const mockLogs = [\n          { id: 1, time: '09:45:30', level: 'INFO', message: 'System started successfully' },\n          { id: 2, time: '09:45:31', level: 'INFO', message: 'Backend connection established' },\n          { id: 3, time: '09:45:32', level: 'INFO', message: 'Profile service initialized' },\n          { id: 4, time: '09:45:33', level: 'INFO', message: 'Ready for automation tasks' }\n        ];\n        setLogs(mockLogs);\n\n      } catch (error) {\n        console.error('Failed to load data:', error);\n        // Add error log\n        setLogs(prev => [...prev, {\n          id: Date.now(),\n          time: new Date().toLocaleTimeString(),\n          level: 'ERROR',\n          message: `Failed to load profiles: ${error.message}`\n        }]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadData();\n\n    // Set up profile service listeners\n    const handleProfileEvent = (event, data) => {\n      const timestamp = new Date().toLocaleTimeString();\n      let logMessage = '';\n\n      switch (event) {\n        case 'profiles_loaded':\n          setProfiles(data);\n          logMessage = `Loaded ${data.length} profiles from backend`;\n          break;\n        case 'profile_created':\n          setProfiles(profileService.getProfiles());\n          logMessage = `Profile \"${data.username}\" created successfully`;\n          break;\n        case 'profile_updated':\n          setProfiles(profileService.getProfiles());\n          logMessage = `Profile \"${data.username}\" updated`;\n          break;\n        case 'profile_deleted':\n          setProfiles(profileService.getProfiles());\n          logMessage = `Profile deleted (ID: ${data})`;\n          break;\n        case 'login_started':\n          logMessage = `Login started for profile ID: ${data}`;\n          break;\n        case 'login_completed':\n          logMessage = `Login completed for profile ID: ${data}`;\n          break;\n        case 'automation_started':\n          logMessage = `Automation started for profile ID: ${data}`;\n          break;\n        case 'automation_paused':\n          logMessage = `Automation paused for profile ID: ${data}`;\n          break;\n        case 'automation_stopped':\n          logMessage = `Automation stopped for profile ID: ${data}`;\n          break;\n        default:\n          return;\n      }\n\n      if (logMessage) {\n        setLogs(prev => [...prev, {\n          id: Date.now(),\n          time: timestamp,\n          level: 'INFO',\n          message: logMessage\n        }]);\n      }\n    };\n\n    profileService.addListener(handleProfileEvent);\n\n    // Cleanup\n    return () => {\n      profileService.removeListener(handleProfileEvent);\n    };\n  }, []);\n\n  // Handle profile actions\n  const handleProfileAction = async (profileId, action) => {\n    try {\n      let result;\n\n      switch (action) {\n        case 'login':\n          result = await profileService.startLogin(profileId);\n          break;\n        case 'complete':\n          result = await profileService.completeLogin(profileId);\n          break;\n        case 'start':\n          result = await profileService.startAutomation(profileId);\n          break;\n        case 'pause':\n          result = await profileService.pauseAutomation(profileId);\n          break;\n        case 'stop':\n          result = await profileService.stopAutomation(profileId);\n          break;\n        default:\n          throw new Error(`Unknown action: ${action}`);\n      }\n\n      console.log(`Action ${action} for profile ${profileId}:`, result);\n    } catch (error) {\n      console.error(`Failed to execute action ${action} for profile ${profileId}:`, error);\n\n      // Add error log\n      setLogs(prev => [...prev, {\n        id: Date.now(),\n        time: new Date().toLocaleTimeString(),\n        level: 'ERROR',\n        message: `Failed to ${action} profile ${profileId}: ${error.message}`\n      }]);\n    }\n  };\n\n  const handleProfileSelect = (profileId, isSelected) => {\n    setSelectedProfiles(prev => {\n      if (isSelected) {\n        return [...prev, profileId];\n      } else {\n        return prev.filter(id => id !== profileId);\n      }\n    });\n  };\n\n  const handleSelectAll = (isSelected) => {\n    if (isSelected) {\n      setSelectedProfiles(profiles.map(p => p.id));\n    } else {\n      setSelectedProfiles([]);\n    }\n  };\n\n  const handleBulkAction = async (action) => {\n    if (selectedProfiles.length === 0) {\n      return;\n    }\n\n    try {\n      console.log(`Bulk action ${action} for profiles:`, selectedProfiles);\n      const results = await profileService.bulkAction(selectedProfiles, action);\n\n      // Log results\n      const successCount = results.filter(r => r.success).length;\n      const failCount = results.filter(r => !r.success).length;\n\n      setLogs(prev => [...prev, {\n        id: Date.now(),\n        time: new Date().toLocaleTimeString(),\n        level: 'INFO',\n        message: `Bulk ${action}: ${successCount} successful, ${failCount} failed`\n      }]);\n\n    } catch (error) {\n      console.error(`Failed to execute bulk action ${action}:`, error);\n      setLogs(prev => [...prev, {\n        id: Date.now(),\n        time: new Date().toLocaleTimeString(),\n        level: 'ERROR',\n        message: `Bulk ${action} failed: ${error.message}`\n      }]);\n    }\n  };\n\n  const handleCreateProfile = async (profileData) => {\n    try {\n      console.log('Creating profile:', profileData);\n\n      // Create profile using service\n      await profileService.createProfile(profileData);\n      setShowProfileForm(false);\n\n    } catch (error) {\n      console.error('Failed to create profile:', error);\n\n      // Add error log\n      setLogs(prev => [...prev, {\n        id: Date.now(),\n        time: new Date().toLocaleTimeString(),\n        level: 'ERROR',\n        message: `Failed to create profile \"${profileData.profileName}\": ${error.message}`\n      }]);\n\n      // Show error to user (you might want to add a toast notification here)\n      alert(`Failed to create profile: ${error.message}`);\n    }\n  };\n\n  const handleClearLogs = () => {\n    setLogs([]);\n  };\n\n  const handleExportLogs = () => {\n    console.log('Exporting logs...');\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  // Calculate stats\n  const totalProfiles = profiles.length;\n  const activeProfiles = profiles.filter(p => p.status === 'running').length;\n  const totalFollows = profiles.reduce((sum, p) => sum + (p.followsToday || 0), 0);\n  const successRate = totalProfiles > 0 ? Math.round((activeProfiles / totalProfiles) * 100) : 0;\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <StatsCard\n          title=\"Total Profiles\"\n          value={totalProfiles}\n          icon={FiUsers}\n          color=\"blue\"\n          trend=\"+12%\"\n        />\n        <StatsCard\n          title=\"Active Tasks\"\n          value={activeProfiles}\n          icon={FiActivity}\n          color=\"green\"\n          trend=\"+5%\"\n        />\n        <StatsCard\n          title=\"Follows Today\"\n          value={totalFollows}\n          icon={FiTarget}\n          color=\"purple\"\n          trend=\"+23%\"\n        />\n        <StatsCard\n          title=\"Success Rate\"\n          value={`${successRate}%`}\n          icon={FiTrendingUp}\n          color=\"orange\"\n          trend=\"+8%\"\n        />\n      </div>\n\n      {/* Main Content Grid */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Left Column - Profile Management */}\n        <div className=\"lg:col-span-2 space-y-6\">\n          {/* Profile Controls */}\n          <div className=\"bg-white rounded-lg shadow-sm\">\n            <div className=\"p-4 border-b border-gray-200\">\n              <div className=\"flex items-center justify-between\">\n                <h2 className=\"text-lg font-semibold text-gray-900\">\n                  Profile Management ({totalProfiles})\n                </h2>\n                <div className=\"flex items-center space-x-2\">\n                  <button\n                    onClick={() => setShowProfileForm(true)}\n                    className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2\"\n                  >\n                    <FiPlus className=\"w-4 h-4\" />\n                    <span>New Profile</span>\n                  </button>\n                  <button\n                    onClick={() => handleBulkAction('start')}\n                    disabled={selectedProfiles.length === 0}\n                    className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed\"\n                  >\n                    <FiPlay className=\"w-4 h-4\" />\n                    <span>Start All</span>\n                  </button>\n                  <button\n                    onClick={() => handleBulkAction('stop')}\n                    disabled={selectedProfiles.length === 0}\n                    className=\"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed\"\n                  >\n                    <FiStopCircle className=\"w-4 h-4\" />\n                    <span>Stop All</span>\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            {/* Profile Table */}\n            <ProfileTable\n              profiles={profiles}\n              selectedProfiles={selectedProfiles}\n              onProfileSelect={handleProfileSelect}\n              onProfileAction={handleProfileAction}\n              onSelectAll={handleSelectAll}\n            />\n          </div>\n\n          {/* Tasks Overview */}\n          <TasksOverview />\n        </div>\n\n        {/* Right Column - Sidebar Content */}\n        <div className=\"space-y-6\">\n          {/* System Status */}\n          <SystemStatus />\n\n          {/* Recent Activity */}\n          <RecentActivity activities={logs.slice(-5)} />\n\n          {/* Log Monitor */}\n          <LogMonitor\n            logs={logs}\n            onClearLogs={handleClearLogs}\n            onExportLogs={handleExportLogs}\n          />\n        </div>\n      </div>\n\n      {/* Profile Form Modal */}\n      <ProfileForm\n        isOpen={showProfileForm}\n        onClose={() => setShowProfileForm(false)}\n        onSubmit={handleCreateProfile}\n      />\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,SACEC,MAAM,EACNC,MAAM,EACNC,YAAY,EACZC,OAAO,EACPC,UAAU,EACVC,QAAQ,EACRC,YAAY,QACP,gBAAgB;;AAEvB;AACA,OAAOC,SAAS,MAAM,mCAAmC;AACzD,OAAOC,YAAY,MAAM,sCAAsC;AAC/D,OAAOC,WAAW,MAAM,qCAAqC;AAC7D,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,YAAY,MAAM,sCAAsC;AAC/D,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,cAAc,MAAM,wCAAwC;;AAEnE;AACA,OAAOC,cAAc,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC,YAAY;IAAEC;EAAgB,CAAC,GAAGrB,gBAAgB,CAAC,CAAC;;EAE5D;EACA,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC0B,eAAe,EAAEC,kBAAkB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAE5D,MAAM,CAAC8B,IAAI,EAAEC,OAAO,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACgC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACdqB,YAAY,CAAC,WAAW,CAAC;IACzBC,eAAe,CAAC,8BAA8B,CAAC;EACjD,CAAC,EAAE,CAACD,YAAY,EAAEC,eAAe,CAAC,CAAC;;EAEnC;EACAtB,SAAS,CAAC,MAAM;IACd,MAAMiC,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI;QACFD,UAAU,CAAC,IAAI,CAAC;;QAEhB;QACA,MAAMhB,cAAc,CAACkB,YAAY,CAAC,CAAC;QACnCV,WAAW,CAACR,cAAc,CAACmB,WAAW,CAAC,CAAC,CAAC;;QAEzC;QACA,MAAMC,QAAQ,GAAG,CACf;UAAEC,EAAE,EAAE,CAAC;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE,MAAM;UAAEC,OAAO,EAAE;QAA8B,CAAC,EAClF;UAAEH,EAAE,EAAE,CAAC;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAiC,CAAC,EACrF;UAAEH,EAAE,EAAE,CAAC;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE,MAAM;UAAEC,OAAO,EAAE;QAA8B,CAAC,EAClF;UAAEH,EAAE,EAAE,CAAC;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE,MAAM;UAAEC,OAAO,EAAE;QAA6B,CAAC,CAClF;QACDV,OAAO,CAACM,QAAQ,CAAC;MAEnB,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C;QACAX,OAAO,CAACa,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UACxBN,EAAE,EAAEO,IAAI,CAACC,GAAG,CAAC,CAAC;UACdP,IAAI,EAAE,IAAIM,IAAI,CAAC,CAAC,CAACE,kBAAkB,CAAC,CAAC;UACrCP,KAAK,EAAE,OAAO;UACdC,OAAO,EAAE,4BAA4BC,KAAK,CAACD,OAAO;QACpD,CAAC,CAAC,CAAC;MACL,CAAC,SAAS;QACRR,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDC,QAAQ,CAAC,CAAC;;IAEV;IACA,MAAMc,kBAAkB,GAAGA,CAACC,KAAK,EAAEC,IAAI,KAAK;MAC1C,MAAMC,SAAS,GAAG,IAAIN,IAAI,CAAC,CAAC,CAACE,kBAAkB,CAAC,CAAC;MACjD,IAAIK,UAAU,GAAG,EAAE;MAEnB,QAAQH,KAAK;QACX,KAAK,iBAAiB;UACpBxB,WAAW,CAACyB,IAAI,CAAC;UACjBE,UAAU,GAAG,UAAUF,IAAI,CAACG,MAAM,wBAAwB;UAC1D;QACF,KAAK,iBAAiB;UACpB5B,WAAW,CAACR,cAAc,CAACmB,WAAW,CAAC,CAAC,CAAC;UACzCgB,UAAU,GAAG,YAAYF,IAAI,CAACI,QAAQ,wBAAwB;UAC9D;QACF,KAAK,iBAAiB;UACpB7B,WAAW,CAACR,cAAc,CAACmB,WAAW,CAAC,CAAC,CAAC;UACzCgB,UAAU,GAAG,YAAYF,IAAI,CAACI,QAAQ,WAAW;UACjD;QACF,KAAK,iBAAiB;UACpB7B,WAAW,CAACR,cAAc,CAACmB,WAAW,CAAC,CAAC,CAAC;UACzCgB,UAAU,GAAG,wBAAwBF,IAAI,GAAG;UAC5C;QACF,KAAK,eAAe;UAClBE,UAAU,GAAG,iCAAiCF,IAAI,EAAE;UACpD;QACF,KAAK,iBAAiB;UACpBE,UAAU,GAAG,mCAAmCF,IAAI,EAAE;UACtD;QACF,KAAK,oBAAoB;UACvBE,UAAU,GAAG,sCAAsCF,IAAI,EAAE;UACzD;QACF,KAAK,mBAAmB;UACtBE,UAAU,GAAG,qCAAqCF,IAAI,EAAE;UACxD;QACF,KAAK,oBAAoB;UACvBE,UAAU,GAAG,sCAAsCF,IAAI,EAAE;UACzD;QACF;UACE;MACJ;MAEA,IAAIE,UAAU,EAAE;QACdrB,OAAO,CAACa,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UACxBN,EAAE,EAAEO,IAAI,CAACC,GAAG,CAAC,CAAC;UACdP,IAAI,EAAEY,SAAS;UACfX,KAAK,EAAE,MAAM;UACbC,OAAO,EAAEW;QACX,CAAC,CAAC,CAAC;MACL;IACF,CAAC;IAEDnC,cAAc,CAACsC,WAAW,CAACP,kBAAkB,CAAC;;IAE9C;IACA,OAAO,MAAM;MACX/B,cAAc,CAACuC,cAAc,CAACR,kBAAkB,CAAC;IACnD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMS,mBAAmB,GAAG,MAAAA,CAAOC,SAAS,EAAEC,MAAM,KAAK;IACvD,IAAI;MACF,IAAIC,MAAM;MAEV,QAAQD,MAAM;QACZ,KAAK,OAAO;UACVC,MAAM,GAAG,MAAM3C,cAAc,CAAC4C,UAAU,CAACH,SAAS,CAAC;UACnD;QACF,KAAK,UAAU;UACbE,MAAM,GAAG,MAAM3C,cAAc,CAAC6C,aAAa,CAACJ,SAAS,CAAC;UACtD;QACF,KAAK,OAAO;UACVE,MAAM,GAAG,MAAM3C,cAAc,CAAC8C,eAAe,CAACL,SAAS,CAAC;UACxD;QACF,KAAK,OAAO;UACVE,MAAM,GAAG,MAAM3C,cAAc,CAAC+C,eAAe,CAACN,SAAS,CAAC;UACxD;QACF,KAAK,MAAM;UACTE,MAAM,GAAG,MAAM3C,cAAc,CAACgD,cAAc,CAACP,SAAS,CAAC;UACvD;QACF;UACE,MAAM,IAAIQ,KAAK,CAAC,mBAAmBP,MAAM,EAAE,CAAC;MAChD;MAEAhB,OAAO,CAACwB,GAAG,CAAC,UAAUR,MAAM,gBAAgBD,SAAS,GAAG,EAAEE,MAAM,CAAC;IACnE,CAAC,CAAC,OAAOlB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4BiB,MAAM,gBAAgBD,SAAS,GAAG,EAAEhB,KAAK,CAAC;;MAEpF;MACAX,OAAO,CAACa,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QACxBN,EAAE,EAAEO,IAAI,CAACC,GAAG,CAAC,CAAC;QACdP,IAAI,EAAE,IAAIM,IAAI,CAAC,CAAC,CAACE,kBAAkB,CAAC,CAAC;QACrCP,KAAK,EAAE,OAAO;QACdC,OAAO,EAAE,aAAakB,MAAM,YAAYD,SAAS,KAAKhB,KAAK,CAACD,OAAO;MACrE,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAM2B,mBAAmB,GAAGA,CAACV,SAAS,EAAEW,UAAU,KAAK;IACrDxC,mBAAmB,CAACe,IAAI,IAAI;MAC1B,IAAIyB,UAAU,EAAE;QACd,OAAO,CAAC,GAAGzB,IAAI,EAAEc,SAAS,CAAC;MAC7B,CAAC,MAAM;QACL,OAAOd,IAAI,CAAC0B,MAAM,CAAChC,EAAE,IAAIA,EAAE,KAAKoB,SAAS,CAAC;MAC5C;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMa,eAAe,GAAIF,UAAU,IAAK;IACtC,IAAIA,UAAU,EAAE;MACdxC,mBAAmB,CAACL,QAAQ,CAACgD,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACnC,EAAE,CAAC,CAAC;IAC9C,CAAC,MAAM;MACLT,mBAAmB,CAAC,EAAE,CAAC;IACzB;EACF,CAAC;EAED,MAAM6C,gBAAgB,GAAG,MAAOf,MAAM,IAAK;IACzC,IAAI/B,gBAAgB,CAACyB,MAAM,KAAK,CAAC,EAAE;MACjC;IACF;IAEA,IAAI;MACFV,OAAO,CAACwB,GAAG,CAAC,eAAeR,MAAM,gBAAgB,EAAE/B,gBAAgB,CAAC;MACpE,MAAM+C,OAAO,GAAG,MAAM1D,cAAc,CAAC2D,UAAU,CAAChD,gBAAgB,EAAE+B,MAAM,CAAC;;MAEzE;MACA,MAAMkB,YAAY,GAAGF,OAAO,CAACL,MAAM,CAACQ,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC,CAAC1B,MAAM;MAC1D,MAAM2B,SAAS,GAAGL,OAAO,CAACL,MAAM,CAACQ,CAAC,IAAI,CAACA,CAAC,CAACC,OAAO,CAAC,CAAC1B,MAAM;MAExDtB,OAAO,CAACa,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QACxBN,EAAE,EAAEO,IAAI,CAACC,GAAG,CAAC,CAAC;QACdP,IAAI,EAAE,IAAIM,IAAI,CAAC,CAAC,CAACE,kBAAkB,CAAC,CAAC;QACrCP,KAAK,EAAE,MAAM;QACbC,OAAO,EAAE,QAAQkB,MAAM,KAAKkB,YAAY,gBAAgBG,SAAS;MACnE,CAAC,CAAC,CAAC;IAEL,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiCiB,MAAM,GAAG,EAAEjB,KAAK,CAAC;MAChEX,OAAO,CAACa,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QACxBN,EAAE,EAAEO,IAAI,CAACC,GAAG,CAAC,CAAC;QACdP,IAAI,EAAE,IAAIM,IAAI,CAAC,CAAC,CAACE,kBAAkB,CAAC,CAAC;QACrCP,KAAK,EAAE,OAAO;QACdC,OAAO,EAAE,QAAQkB,MAAM,YAAYjB,KAAK,CAACD,OAAO;MAClD,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMwC,mBAAmB,GAAG,MAAOC,WAAW,IAAK;IACjD,IAAI;MACFvC,OAAO,CAACwB,GAAG,CAAC,mBAAmB,EAAEe,WAAW,CAAC;;MAE7C;MACA,MAAMjE,cAAc,CAACkE,aAAa,CAACD,WAAW,CAAC;MAC/CvD,kBAAkB,CAAC,KAAK,CAAC;IAE3B,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;;MAEjD;MACAX,OAAO,CAACa,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QACxBN,EAAE,EAAEO,IAAI,CAACC,GAAG,CAAC,CAAC;QACdP,IAAI,EAAE,IAAIM,IAAI,CAAC,CAAC,CAACE,kBAAkB,CAAC,CAAC;QACrCP,KAAK,EAAE,OAAO;QACdC,OAAO,EAAE,6BAA6ByC,WAAW,CAACE,WAAW,MAAM1C,KAAK,CAACD,OAAO;MAClF,CAAC,CAAC,CAAC;;MAEH;MACA4C,KAAK,CAAC,6BAA6B3C,KAAK,CAACD,OAAO,EAAE,CAAC;IACrD;EACF,CAAC;EAED,MAAM6C,eAAe,GAAGA,CAAA,KAAM;IAC5BvD,OAAO,CAAC,EAAE,CAAC;EACb,CAAC;EAED,MAAMwD,gBAAgB,GAAGA,CAAA,KAAM;IAC7B5C,OAAO,CAACwB,GAAG,CAAC,mBAAmB,CAAC;EAClC,CAAC;EAED,IAAInC,OAAO,EAAE;IACX,oBACEb,OAAA;MAAKqE,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDtE,OAAA;QAAKqE,SAAS,EAAC;MAAgE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC;EAEV;;EAEA;EACA,MAAMC,aAAa,GAAGtE,QAAQ,CAAC6B,MAAM;EACrC,MAAM0C,cAAc,GAAGvE,QAAQ,CAAC8C,MAAM,CAACG,CAAC,IAAIA,CAAC,CAACuB,MAAM,KAAK,SAAS,CAAC,CAAC3C,MAAM;EAC1E,MAAM4C,YAAY,GAAGzE,QAAQ,CAAC0E,MAAM,CAAC,CAACC,GAAG,EAAE1B,CAAC,KAAK0B,GAAG,IAAI1B,CAAC,CAAC2B,YAAY,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAChF,MAAMC,WAAW,GAAGP,aAAa,GAAG,CAAC,GAAGQ,IAAI,CAACC,KAAK,CAAER,cAAc,GAAGD,aAAa,GAAI,GAAG,CAAC,GAAG,CAAC;EAE9F,oBACE3E,OAAA;IAAKqE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBtE,OAAA;MAAKqE,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnEtE,OAAA,CAACT,SAAS;QACR8F,KAAK,EAAC,gBAAgB;QACtBC,KAAK,EAAEX,aAAc;QACrBY,IAAI,EAAEpG,OAAQ;QACdqG,KAAK,EAAC,MAAM;QACZC,KAAK,EAAC;MAAM;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eACF1E,OAAA,CAACT,SAAS;QACR8F,KAAK,EAAC,cAAc;QACpBC,KAAK,EAAEV,cAAe;QACtBW,IAAI,EAAEnG,UAAW;QACjBoG,KAAK,EAAC,OAAO;QACbC,KAAK,EAAC;MAAK;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eACF1E,OAAA,CAACT,SAAS;QACR8F,KAAK,EAAC,eAAe;QACrBC,KAAK,EAAER,YAAa;QACpBS,IAAI,EAAElG,QAAS;QACfmG,KAAK,EAAC,QAAQ;QACdC,KAAK,EAAC;MAAM;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eACF1E,OAAA,CAACT,SAAS;QACR8F,KAAK,EAAC,cAAc;QACpBC,KAAK,EAAE,GAAGJ,WAAW,GAAI;QACzBK,IAAI,EAAEjG,YAAa;QACnBkG,KAAK,EAAC,QAAQ;QACdC,KAAK,EAAC;MAAK;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN1E,OAAA;MAAKqE,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDtE,OAAA;QAAKqE,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBAEtCtE,OAAA;UAAKqE,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAC5CtE,OAAA;YAAKqE,SAAS,EAAC,8BAA8B;YAAAC,QAAA,eAC3CtE,OAAA;cAAKqE,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDtE,OAAA;gBAAIqE,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,GAAC,sBAC9B,EAACK,aAAa,EAAC,GACrC;cAAA;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL1E,OAAA;gBAAKqE,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CtE,OAAA;kBACE0F,OAAO,EAAEA,CAAA,KAAMlF,kBAAkB,CAAC,IAAI,CAAE;kBACxC6D,SAAS,EAAC,6GAA6G;kBAAAC,QAAA,gBAEvHtE,OAAA,CAAChB,MAAM;oBAACqF,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9B1E,OAAA;oBAAAsE,QAAA,EAAM;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACT1E,OAAA;kBACE0F,OAAO,EAAEA,CAAA,KAAMnC,gBAAgB,CAAC,OAAO,CAAE;kBACzCoC,QAAQ,EAAElF,gBAAgB,CAACyB,MAAM,KAAK,CAAE;kBACxCmC,SAAS,EAAC,+JAA+J;kBAAAC,QAAA,gBAEzKtE,OAAA,CAACf,MAAM;oBAACoF,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9B1E,OAAA;oBAAAsE,QAAA,EAAM;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACT1E,OAAA;kBACE0F,OAAO,EAAEA,CAAA,KAAMnC,gBAAgB,CAAC,MAAM,CAAE;kBACxCoC,QAAQ,EAAElF,gBAAgB,CAACyB,MAAM,KAAK,CAAE;kBACxCmC,SAAS,EAAC,2JAA2J;kBAAAC,QAAA,gBAErKtE,OAAA,CAACd,YAAY;oBAACmF,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpC1E,OAAA;oBAAAsE,QAAA,EAAM;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN1E,OAAA,CAACR,YAAY;YACXa,QAAQ,EAAEA,QAAS;YACnBI,gBAAgB,EAAEA,gBAAiB;YACnCmF,eAAe,EAAE3C,mBAAoB;YACrC4C,eAAe,EAAEvD,mBAAoB;YACrCwD,WAAW,EAAE1C;UAAgB;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN1E,OAAA,CAACJ,aAAa;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC,eAGN1E,OAAA;QAAKqE,SAAS,EAAC,WAAW;QAAAC,QAAA,gBAExBtE,OAAA,CAACL,YAAY;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGhB1E,OAAA,CAACH,cAAc;UAACkG,UAAU,EAAEpF,IAAI,CAACqF,KAAK,CAAC,CAAC,CAAC;QAAE;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG9C1E,OAAA,CAACN,UAAU;UACTiB,IAAI,EAAEA,IAAK;UACXsF,WAAW,EAAE9B,eAAgB;UAC7B+B,YAAY,EAAE9B;QAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1E,OAAA,CAACP,WAAW;MACV0G,MAAM,EAAE5F,eAAgB;MACxB6F,OAAO,EAAEA,CAAA,KAAM5F,kBAAkB,CAAC,KAAK,CAAE;MACzC6F,QAAQ,EAAEvC;IAAoB;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACxE,EAAA,CArWID,SAAS;EAAA,QAC6BlB,gBAAgB;AAAA;AAAAuH,EAAA,GADtDrG,SAAS;AAuWf,eAAeA,SAAS;AAAC,IAAAqG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}