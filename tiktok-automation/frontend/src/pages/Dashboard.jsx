/**
 * TikTok Automation Dashboard - Main management interface
 */

import React, { useState, useEffect } from 'react';
import {
  FiPlus,
  FiPlay,
  FiPause,
  FiStopCircle
} from 'react-icons/fi';

// Components
import ProfileTable from '../components/Dashboard/ProfileTable';
import ProfileForm from '../components/Dashboard/ProfileForm';
import LogMonitor from '../components/Dashboard/LogMonitor';

// Services
import profileService from '../services/profileService';

const Dashboard = () => {
  // State management
  const [profiles, setProfiles] = useState([]);
  const [showProfileForm, setShowProfileForm] = useState(false);
  const [selectedProfiles, setSelectedProfiles] = useState([]);
  const [automationSettings, setAutomationSettings] = useState({
    targetProfile: '',
    videosToWatch: 3,
    watchTimeRange: '2000-5000',
    followLimit: 10,
    followDelay: '3000-8000'
  });
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(true);

  // Load data from backend
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);

        // Load profiles from backend
        await profileService.loadProfiles();
        setProfiles(profileService.getProfiles());

        // Mock logs for now - TODO: Load from backend
        const mockLogs = [
          { id: 1, time: '09:45:30', level: 'INFO', message: 'System started successfully' },
          { id: 2, time: '09:45:31', level: 'INFO', message: 'Backend connection established' },
          { id: 3, time: '09:45:32', level: 'INFO', message: 'Profile service initialized' },
          { id: 4, time: '09:45:33', level: 'INFO', message: 'Ready for automation tasks' }
        ];
        setLogs(mockLogs);

      } catch (error) {
        console.error('Failed to load data:', error);
        // Add error log
        setLogs(prev => [...prev, {
          id: Date.now(),
          time: new Date().toLocaleTimeString(),
          level: 'ERROR',
          message: `Failed to load profiles: ${error.message}`
        }]);
      } finally {
        setLoading(false);
      }
    };

    loadData();

    // Set up profile service listeners
    const handleProfileEvent = (event, data) => {
      const timestamp = new Date().toLocaleTimeString();
      let logMessage = '';

      switch (event) {
        case 'profiles_loaded':
          setProfiles(data);
          logMessage = `Loaded ${data.length} profiles from backend`;
          break;
        case 'profile_created':
          setProfiles(profileService.getProfiles());
          logMessage = `Profile "${data.username}" created successfully`;
          break;
        case 'profile_updated':
          setProfiles(profileService.getProfiles());
          logMessage = `Profile "${data.username}" updated`;
          break;
        case 'profile_deleted':
          setProfiles(profileService.getProfiles());
          logMessage = `Profile deleted (ID: ${data})`;
          break;
        case 'login_started':
          logMessage = `Login started for profile ID: ${data}`;
          break;
        case 'login_completed':
          logMessage = `Login completed for profile ID: ${data}`;
          break;
        case 'automation_started':
          logMessage = `Automation started for profile ID: ${data}`;
          break;
        case 'automation_paused':
          logMessage = `Automation paused for profile ID: ${data}`;
          break;
        case 'automation_stopped':
          logMessage = `Automation stopped for profile ID: ${data}`;
          break;
        default:
          return;
      }

      if (logMessage) {
        setLogs(prev => [...prev, {
          id: Date.now(),
          time: timestamp,
          level: 'INFO',
          message: logMessage
        }]);
      }
    };

    profileService.addListener(handleProfileEvent);

    // Cleanup
    return () => {
      profileService.removeListener(handleProfileEvent);
    };
  }, []);

  // Handle profile actions
  const handleProfileAction = async (profileId, action) => {
    try {
      let result;

      switch (action) {
        case 'login':
          result = await profileService.startLogin(profileId);
          break;
        case 'complete':
          result = await profileService.completeLogin(profileId);
          break;
        case 'start':
          result = await profileService.startAutomation(profileId);
          break;
        case 'pause':
          result = await profileService.pauseAutomation(profileId);
          break;
        case 'stop':
          result = await profileService.stopAutomation(profileId);
          break;
        default:
          throw new Error(`Unknown action: ${action}`);
      }

      console.log(`Action ${action} for profile ${profileId}:`, result);
    } catch (error) {
      console.error(`Failed to execute action ${action} for profile ${profileId}:`, error);

      // Add error log
      setLogs(prev => [...prev, {
        id: Date.now(),
        time: new Date().toLocaleTimeString(),
        level: 'ERROR',
        message: `Failed to ${action} profile ${profileId}: ${error.message}`
      }]);
    }
  };

  const handleProfileSelect = (profileId, isSelected) => {
    setSelectedProfiles(prev => {
      if (isSelected) {
        return [...prev, profileId];
      } else {
        return prev.filter(id => id !== profileId);
      }
    });
  };

  const handleSelectAll = (isSelected) => {
    if (isSelected) {
      setSelectedProfiles(profiles.map(p => p.id));
    } else {
      setSelectedProfiles([]);
    }
  };

  const handleBulkAction = async (action) => {
    if (selectedProfiles.length === 0) {
      return;
    }

    try {
      console.log(`Bulk action ${action} for profiles:`, selectedProfiles);
      const results = await profileService.bulkAction(selectedProfiles, action);

      // Log results
      const successCount = results.filter(r => r.success).length;
      const failCount = results.filter(r => !r.success).length;

      setLogs(prev => [...prev, {
        id: Date.now(),
        time: new Date().toLocaleTimeString(),
        level: 'INFO',
        message: `Bulk ${action}: ${successCount} successful, ${failCount} failed`
      }]);

    } catch (error) {
      console.error(`Failed to execute bulk action ${action}:`, error);
      setLogs(prev => [...prev, {
        id: Date.now(),
        time: new Date().toLocaleTimeString(),
        level: 'ERROR',
        message: `Bulk ${action} failed: ${error.message}`
      }]);
    }
  };

  const handleCreateProfile = async (profileData) => {
    try {
      console.log('Creating profile:', profileData);

      // Create profile using service
      await profileService.createProfile(profileData);
      setShowProfileForm(false);

    } catch (error) {
      console.error('Failed to create profile:', error);

      // Add error log
      setLogs(prev => [...prev, {
        id: Date.now(),
        time: new Date().toLocaleTimeString(),
        level: 'ERROR',
        message: `Failed to create profile "${profileData.profileName}": ${error.message}`
      }]);

      // Show error to user (you might want to add a toast notification here)
      alert(`Failed to create profile: ${error.message}`);
    }
  };

  const handleClearLogs = () => {
    setLogs([]);
  };

  const handleExportLogs = () => {
    console.log('Exporting logs...');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 via-purple-700 to-purple-800">
      {/* Header */}
      <div className="bg-white/10 backdrop-blur-sm border-b border-white/20">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-white">TikTok Automation Dashboard</h1>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 bg-green-500 px-3 py-1 rounded-full">
                <div className="w-2 h-2 bg-white rounded-full"></div>
                <span className="text-white text-sm font-medium">Online</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Left Panel - Profile Management */}
          <div className="lg:col-span-3 space-y-6">
            {/* Profile Controls */}
            <div className="bg-white rounded-lg shadow-sm">
              <div className="p-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold text-gray-900">Quản lý Tài khoản (3)</h2>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setShowProfileForm(true)}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                    >
                      <FiPlus className="w-4 h-4" />
                      <span>Tạo mới</span>
                    </button>
                    <button
                      onClick={() => handleBulkAction('start')}
                      className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
                    >
                      <FiPlay className="w-4 h-4" />
                      <span>Chạy tất cả</span>
                    </button>
                    <button
                      onClick={() => handleBulkAction('stop')}
                      className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2"
                    >
                      <FiStopCircle className="w-4 h-4" />
                      <span>Dừng tất cả</span>
                    </button>
                  </div>
                </div>
              </div>

              {/* Profile Table */}
              <ProfileTable
                profiles={profiles}
                selectedProfiles={selectedProfiles}
                onProfileSelect={handleProfileSelect}
                onProfileAction={handleProfileAction}
                onSelectAll={handleSelectAll}
              />
            </div>
          </div>

          {/* Right Panel - Settings and Logs */}
          <div className="space-y-6">
            {/* Automation Settings */}
            <div className="bg-white rounded-lg shadow-sm p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Điều khiển & Cài đặt</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Ngày đổi theo</label>
                  <div className="flex space-x-2">
                    <button className="px-3 py-1 bg-blue-600 text-white rounded text-sm">Hôm nay</button>
                    <button className="px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm">Hôm qua</button>
                    <button className="px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm">Tuần này</button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Điều khiển ({selectedProfiles.length} tài khoản đang chọn)
                  </label>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleBulkAction('start')}
                      disabled={selectedProfiles.length === 0}
                      className="px-3 py-1 bg-green-600 text-white rounded text-sm flex items-center space-x-1 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <FiPlay className="w-3 h-3" />
                      <span>Bắt đầu</span>
                    </button>
                    <button
                      onClick={() => handleBulkAction('pause')}
                      disabled={selectedProfiles.length === 0}
                      className="px-3 py-1 bg-yellow-600 text-white rounded text-sm flex items-center space-x-1 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <FiPause className="w-3 h-3" />
                      <span>Tạm dừng</span>
                    </button>
                    <button
                      onClick={() => handleBulkAction('stop')}
                      disabled={selectedProfiles.length === 0}
                      className="px-3 py-1 bg-red-600 text-white rounded text-sm flex items-center space-x-1 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <FiStopCircle className="w-3 h-3" />
                      <span>Dừng hẳn</span>
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Cài đặt kịch bản</label>
                  <div className="space-y-3">
                    <div>
                      <label className="block text-xs text-gray-600 mb-1">Link profile đối thủ</label>
                      <input
                        type="text"
                        placeholder="https://www.tiktok.com/@username"
                        className="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <label className="block text-xs text-gray-600 mb-1">Số video xem</label>
                        <input type="number" defaultValue="3" className="w-full px-3 py-2 border border-gray-300 rounded text-sm" />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-600 mb-1">Thời gian xem (giây)</label>
                        <input type="text" defaultValue="30" className="w-full px-3 py-2 border border-gray-300 rounded text-sm" />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <label className="block text-xs text-gray-600 mb-1">Follow tối đa/ngày</label>
                        <input type="number" defaultValue="10" className="w-full px-3 py-2 border border-gray-300 rounded text-sm" />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-600 mb-1">Khoảng cách giữa phiên (giây)</label>
                        <input type="text" defaultValue="3600" className="w-full px-3 py-2 border border-gray-300 rounded text-sm" />
                      </div>
                    </div>

                    <button className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 transition-colors">
                      Cập nhật cài đặt
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Log Monitor */}
            <LogMonitor
              logs={logs}
              onClearLogs={handleClearLogs}
              onExportLogs={handleExportLogs}
            />
          </div>
        </div>
      </div>

      {/* Profile Form Modal */}
      <ProfileForm
        isOpen={showProfileForm}
        onClose={() => setShowProfileForm(false)}
        onSubmit={handleCreateProfile}
      />
    </div>
  );
};

export default Dashboard;
