{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Dashboard/SystemStatus.jsx\",\n  _s = $RefreshSig$();\n/**\n * System Status Component for Dashboard\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { FiServer, FiWifi, FiCpu, FiHardDrive, FiActivity, FiCheckCircle, FiAlertTriangle, FiXCircle } from 'react-icons/fi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SystemStatus = () => {\n  _s();\n  const [systemData, setSystemData] = useState({\n    backend: 'connecting',\n    database: 'connecting',\n    memory: {\n      used: 0,\n      total: 0,\n      percent: 0\n    },\n    activeBrowsers: 0,\n    activeConnections: 0\n  });\n  const [loading, setLoading] = useState(true);\n\n  // Fetch system status\n  useEffect(() => {\n    const fetchSystemStatus = async () => {\n      try {\n        var _data$websocket;\n        let baseUrl = 'http://localhost:8000';\n        if (window.electronAPI) {\n          baseUrl = await window.electronAPI.getBackendUrl();\n        }\n        const response = await fetch(`${baseUrl}/api/v1/system/status`);\n        const data = await response.json();\n        const memoryData = data.memory_usage || {\n          used: 0,\n          total: 0,\n          percent: 0\n        };\n        setSystemData({\n          backend: data.status === 'healthy' ? 'connected' : 'error',\n          database: data.database === 'connected' ? 'connected' : 'error',\n          memory: {\n            used: memoryData.used || 0,\n            total: memoryData.total || 0,\n            percent: memoryData.percent || 0\n          },\n          activeBrowsers: data.browser_instances || 0,\n          activeConnections: ((_data$websocket = data.websocket) === null || _data$websocket === void 0 ? void 0 : _data$websocket.connections) || 0\n        });\n      } catch (error) {\n        console.error('Failed to fetch system status:', error);\n        setSystemData(prev => ({\n          ...prev,\n          backend: 'error',\n          database: 'error'\n        }));\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchSystemStatus();\n    const interval = setInterval(fetchSystemStatus, 15000); // Refresh every 15 seconds\n    return () => clearInterval(interval);\n  }, []);\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'connected':\n        return /*#__PURE__*/_jsxDEV(FiCheckCircle, {\n          className: \"w-4 h-4 text-green-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 16\n        }, this);\n      case 'warning':\n        return /*#__PURE__*/_jsxDEV(FiAlertTriangle, {\n          className: \"w-4 h-4 text-yellow-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 16\n        }, this);\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(FiXCircle, {\n          className: \"w-4 h-4 text-red-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FiActivity, {\n          className: \"w-4 h-4 text-gray-600 animate-pulse\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'connected':\n        return 'text-green-600';\n      case 'warning':\n        return 'text-yellow-600';\n      case 'error':\n        return 'text-red-600';\n      default:\n        return 'text-gray-600';\n    }\n  };\n  const getStatusText = status => {\n    switch (status) {\n      case 'connected':\n        return 'Online';\n      case 'warning':\n        return 'Warning';\n      case 'error':\n        return 'Offline';\n      default:\n        return 'Connecting...';\n    }\n  };\n  const getMemoryStatus = percent => {\n    if (percent > 90) return 'error';\n    if (percent > 70) return 'warning';\n    return 'connected';\n  };\n  const statusItems = [{\n    label: 'Backend Server',\n    status: systemData.backend,\n    icon: FiServer,\n    description: 'Python FastAPI server'\n  }, {\n    label: 'Database',\n    status: systemData.database,\n    icon: FiHardDrive,\n    description: 'SQLite database connection'\n  }, {\n    label: 'Memory Usage',\n    status: getMemoryStatus(systemData.memory.percent),\n    icon: FiCpu,\n    description: `${systemData.memory.percent.toFixed(1)}% used`,\n    value: `${systemData.memory.used} MB`\n  }, {\n    label: 'WebSocket',\n    status: systemData.activeConnections > 0 ? 'connected' : 'warning',\n    icon: FiWifi,\n    description: `${systemData.activeConnections} active connections`\n  }];\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-lg font-semibold text-gray-900 mb-4\",\n        children: \"System Status\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [1, 2, 3, 4].map(i => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-pulse flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 bg-gray-200 rounded\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-4 bg-gray-200 rounded w-3/4 mb-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-3 bg-gray-200 rounded w-1/2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this)]\n        }, i, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-lg font-semibold text-gray-900 mb-6\",\n      children: \"System Status\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: statusItems.map((item, index) => {\n        const Icon = item.icon;\n        return /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: 20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            delay: index * 0.1\n          },\n          className: \"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(Icon, {\n              className: \"w-4 h-4 text-gray-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-medium text-gray-900\",\n                children: item.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [getStatusIcon(item.status), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `text-sm font-medium ${getStatusColor(item.status)}`,\n                  children: getStatusText(item.status)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mt-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: item.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 19\n              }, this), item.value && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-500\",\n                children: item.value\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 15\n          }, this)]\n        }, item.label, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6 p-4 bg-gray-50 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm font-medium text-gray-700\",\n          children: \"Memory Usage\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-gray-600\",\n          children: [systemData.memory.used, \" MB / \", systemData.memory.total, \" MB\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full bg-gray-200 rounded-full h-2\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          className: `h-2 rounded-full ${systemData.memory.percent > 90 ? 'bg-red-500' : systemData.memory.percent > 70 ? 'bg-yellow-500' : 'bg-green-500'}`,\n          initial: {\n            width: 0\n          },\n          animate: {\n            width: `${systemData.memory.percent}%`\n          },\n          transition: {\n            duration: 0.5\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6 grid grid-cols-2 gap-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center p-3 bg-blue-50 rounded-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-bold text-blue-600\",\n          children: systemData.activeBrowsers\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-blue-600\",\n          children: \"Active Browsers\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center p-3 bg-green-50 rounded-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-bold text-green-600\",\n          children: systemData.activeConnections\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-green-600\",\n          children: \"Connections\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 162,\n    columnNumber: 5\n  }, this);\n};\n_s(SystemStatus, \"7bdienBNPaTfJ/mk2S5Qkgq+fEU=\");\n_c = SystemStatus;\nexport default SystemStatus;\nvar _c;\n$RefreshReg$(_c, \"SystemStatus\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "FiServer", "FiWifi", "FiCpu", "FiHardDrive", "FiActivity", "FiCheckCircle", "FiAlertTriangle", "FiXCircle", "jsxDEV", "_jsxDEV", "SystemStatus", "_s", "systemData", "setSystemData", "backend", "database", "memory", "used", "total", "percent", "activeBrowsers", "activeConnections", "loading", "setLoading", "fetchSystemStatus", "_data$websocket", "baseUrl", "window", "electronAPI", "getBackendUrl", "response", "fetch", "data", "json", "memoryData", "memory_usage", "status", "browser_instances", "websocket", "connections", "error", "console", "prev", "interval", "setInterval", "clearInterval", "getStatusIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getStatusColor", "getStatusText", "getMemoryStatus", "statusItems", "label", "icon", "description", "toFixed", "value", "children", "map", "i", "item", "index", "Icon", "div", "initial", "opacity", "x", "animate", "transition", "delay", "width", "duration", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Projects/camoufox/tiktok-automation/frontend/src/components/Dashboard/SystemStatus.jsx"], "sourcesContent": ["/**\n * System Status Component for Dashboard\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  FiServer,\n  FiWifi,\n  FiCpu,\n  FiHardDrive,\n  FiActivity,\n  FiCheckCircle,\n  FiAlertTriangle,\n  FiXCircle\n} from 'react-icons/fi';\n\nconst SystemStatus = () => {\n  const [systemData, setSystemData] = useState({\n    backend: 'connecting',\n    database: 'connecting',\n    memory: { used: 0, total: 0, percent: 0 },\n    activeBrowsers: 0,\n    activeConnections: 0\n  });\n  const [loading, setLoading] = useState(true);\n\n  // Fetch system status\n  useEffect(() => {\n    const fetchSystemStatus = async () => {\n      try {\n        let baseUrl = 'http://localhost:8000';\n        if (window.electronAPI) {\n          baseUrl = await window.electronAPI.getBackendUrl();\n        }\n\n        const response = await fetch(`${baseUrl}/api/v1/system/status`);\n        const data = await response.json();\n        \n        const memoryData = data.memory_usage || { used: 0, total: 0, percent: 0 };\n        setSystemData({\n          backend: data.status === 'healthy' ? 'connected' : 'error',\n          database: data.database === 'connected' ? 'connected' : 'error',\n          memory: {\n            used: memoryData.used || 0,\n            total: memoryData.total || 0,\n            percent: memoryData.percent || 0\n          },\n          activeBrowsers: data.browser_instances || 0,\n          activeConnections: data.websocket?.connections || 0\n        });\n      } catch (error) {\n        console.error('Failed to fetch system status:', error);\n        setSystemData(prev => ({\n          ...prev,\n          backend: 'error',\n          database: 'error'\n        }));\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchSystemStatus();\n    const interval = setInterval(fetchSystemStatus, 15000); // Refresh every 15 seconds\n    return () => clearInterval(interval);\n  }, []);\n\n  const getStatusIcon = (status) => {\n    switch (status) {\n      case 'connected':\n        return <FiCheckCircle className=\"w-4 h-4 text-green-600\" />;\n      case 'warning':\n        return <FiAlertTriangle className=\"w-4 h-4 text-yellow-600\" />;\n      case 'error':\n        return <FiXCircle className=\"w-4 h-4 text-red-600\" />;\n      default:\n        return <FiActivity className=\"w-4 h-4 text-gray-600 animate-pulse\" />;\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'connected':\n        return 'text-green-600';\n      case 'warning':\n        return 'text-yellow-600';\n      case 'error':\n        return 'text-red-600';\n      default:\n        return 'text-gray-600';\n    }\n  };\n\n  const getStatusText = (status) => {\n    switch (status) {\n      case 'connected':\n        return 'Online';\n      case 'warning':\n        return 'Warning';\n      case 'error':\n        return 'Offline';\n      default:\n        return 'Connecting...';\n    }\n  };\n\n  const getMemoryStatus = (percent) => {\n    if (percent > 90) return 'error';\n    if (percent > 70) return 'warning';\n    return 'connected';\n  };\n\n  const statusItems = [\n    {\n      label: 'Backend Server',\n      status: systemData.backend,\n      icon: FiServer,\n      description: 'Python FastAPI server'\n    },\n    {\n      label: 'Database',\n      status: systemData.database,\n      icon: FiHardDrive,\n      description: 'SQLite database connection'\n    },\n    {\n      label: 'Memory Usage',\n      status: getMemoryStatus(systemData.memory.percent),\n      icon: FiCpu,\n      description: `${systemData.memory.percent.toFixed(1)}% used`,\n      value: `${systemData.memory.used} MB`\n    },\n    {\n      label: 'WebSocket',\n      status: systemData.activeConnections > 0 ? 'connected' : 'warning',\n      icon: FiWifi,\n      description: `${systemData.activeConnections} active connections`\n    }\n  ];\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">System Status</h2>\n        <div className=\"space-y-4\">\n          {[1, 2, 3, 4].map((i) => (\n            <div key={i} className=\"animate-pulse flex items-center space-x-3\">\n              <div className=\"w-8 h-8 bg-gray-200 rounded\"></div>\n              <div className=\"flex-1\">\n                <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-1\"></div>\n                <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n      <h2 className=\"text-lg font-semibold text-gray-900 mb-6\">System Status</h2>\n\n      <div className=\"space-y-4\">\n        {statusItems.map((item, index) => {\n          const Icon = item.icon;\n          return (\n            <motion.div\n              key={item.label}\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: index * 0.1 }}\n              className=\"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors\"\n            >\n              <div className=\"w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center\">\n                <Icon className=\"w-4 h-4 text-gray-600\" />\n              </div>\n              \n              <div className=\"flex-1\">\n                <div className=\"flex items-center justify-between\">\n                  <h3 className=\"font-medium text-gray-900\">{item.label}</h3>\n                  <div className=\"flex items-center space-x-2\">\n                    {getStatusIcon(item.status)}\n                    <span className={`text-sm font-medium ${getStatusColor(item.status)}`}>\n                      {getStatusText(item.status)}\n                    </span>\n                  </div>\n                </div>\n                <div className=\"flex items-center justify-between mt-1\">\n                  <p className=\"text-sm text-gray-600\">{item.description}</p>\n                  {item.value && (\n                    <span className=\"text-sm text-gray-500\">{item.value}</span>\n                  )}\n                </div>\n              </div>\n            </motion.div>\n          );\n        })}\n      </div>\n\n      {/* Memory Usage Progress Bar */}\n      <div className=\"mt-6 p-4 bg-gray-50 rounded-lg\">\n        <div className=\"flex items-center justify-between mb-2\">\n          <span className=\"text-sm font-medium text-gray-700\">Memory Usage</span>\n          <span className=\"text-sm text-gray-600\">\n            {systemData.memory.used} MB / {systemData.memory.total} MB\n          </span>\n        </div>\n        <div className=\"w-full bg-gray-200 rounded-full h-2\">\n          <motion.div\n            className={`h-2 rounded-full ${\n              systemData.memory.percent > 90\n                ? 'bg-red-500'\n                : systemData.memory.percent > 70\n                ? 'bg-yellow-500'\n                : 'bg-green-500'\n            }`}\n            initial={{ width: 0 }}\n            animate={{ width: `${systemData.memory.percent}%` }}\n            transition={{ duration: 0.5 }}\n          />\n        </div>\n      </div>\n\n      {/* Quick Stats */}\n      <div className=\"mt-6 grid grid-cols-2 gap-4\">\n        <div className=\"text-center p-3 bg-blue-50 rounded-lg\">\n          <div className=\"text-2xl font-bold text-blue-600\">\n            {systemData.activeBrowsers}\n          </div>\n          <div className=\"text-sm text-blue-600\">Active Browsers</div>\n        </div>\n        <div className=\"text-center p-3 bg-green-50 rounded-lg\">\n          <div className=\"text-2xl font-bold text-green-600\">\n            {systemData.activeConnections}\n          </div>\n          <div className=\"text-sm text-green-600\">Connections</div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SystemStatus;\n"], "mappings": ";;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,QAAQ,EACRC,MAAM,EACNC,KAAK,EACLC,WAAW,EACXC,UAAU,EACVC,aAAa,EACbC,eAAe,EACfC,SAAS,QACJ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC;IAC3CiB,OAAO,EAAE,YAAY;IACrBC,QAAQ,EAAE,YAAY;IACtBC,MAAM,EAAE;MAAEC,IAAI,EAAE,CAAC;MAAEC,KAAK,EAAE,CAAC;MAAEC,OAAO,EAAE;IAAE,CAAC;IACzCC,cAAc,EAAE,CAAC;IACjBC,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,MAAM0B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,IAAI;QAAA,IAAAC,eAAA;QACF,IAAIC,OAAO,GAAG,uBAAuB;QACrC,IAAIC,MAAM,CAACC,WAAW,EAAE;UACtBF,OAAO,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,aAAa,CAAC,CAAC;QACpD;QAEA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,OAAO,uBAAuB,CAAC;QAC/D,MAAMM,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAElC,MAAMC,UAAU,GAAGF,IAAI,CAACG,YAAY,IAAI;UAAElB,IAAI,EAAE,CAAC;UAAEC,KAAK,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAE,CAAC;QACzEN,aAAa,CAAC;UACZC,OAAO,EAAEkB,IAAI,CAACI,MAAM,KAAK,SAAS,GAAG,WAAW,GAAG,OAAO;UAC1DrB,QAAQ,EAAEiB,IAAI,CAACjB,QAAQ,KAAK,WAAW,GAAG,WAAW,GAAG,OAAO;UAC/DC,MAAM,EAAE;YACNC,IAAI,EAAEiB,UAAU,CAACjB,IAAI,IAAI,CAAC;YAC1BC,KAAK,EAAEgB,UAAU,CAAChB,KAAK,IAAI,CAAC;YAC5BC,OAAO,EAAEe,UAAU,CAACf,OAAO,IAAI;UACjC,CAAC;UACDC,cAAc,EAAEY,IAAI,CAACK,iBAAiB,IAAI,CAAC;UAC3ChB,iBAAiB,EAAE,EAAAI,eAAA,GAAAO,IAAI,CAACM,SAAS,cAAAb,eAAA,uBAAdA,eAAA,CAAgBc,WAAW,KAAI;QACpD,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD3B,aAAa,CAAC6B,IAAI,KAAK;UACrB,GAAGA,IAAI;UACP5B,OAAO,EAAE,OAAO;UAChBC,QAAQ,EAAE;QACZ,CAAC,CAAC,CAAC;MACL,CAAC,SAAS;QACRQ,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDC,iBAAiB,CAAC,CAAC;IACnB,MAAMmB,QAAQ,GAAGC,WAAW,CAACpB,iBAAiB,EAAE,KAAK,CAAC,CAAC,CAAC;IACxD,OAAO,MAAMqB,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,aAAa,GAAIV,MAAM,IAAK;IAChC,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,oBAAO3B,OAAA,CAACJ,aAAa;UAAC0C,SAAS,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7D,KAAK,SAAS;QACZ,oBAAO1C,OAAA,CAACH,eAAe;UAACyC,SAAS,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAChE,KAAK,OAAO;QACV,oBAAO1C,OAAA,CAACF,SAAS;UAACwC,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvD;QACE,oBAAO1C,OAAA,CAACL,UAAU;UAAC2C,SAAS,EAAC;QAAqC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACzE;EACF,CAAC;EAED,MAAMC,cAAc,GAAIhB,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,gBAAgB;MACzB,KAAK,SAAS;QACZ,OAAO,iBAAiB;MAC1B,KAAK,OAAO;QACV,OAAO,cAAc;MACvB;QACE,OAAO,eAAe;IAC1B;EACF,CAAC;EAED,MAAMiB,aAAa,GAAIjB,MAAM,IAAK;IAChC,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,QAAQ;MACjB,KAAK,SAAS;QACZ,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,SAAS;MAClB;QACE,OAAO,eAAe;IAC1B;EACF,CAAC;EAED,MAAMkB,eAAe,GAAInC,OAAO,IAAK;IACnC,IAAIA,OAAO,GAAG,EAAE,EAAE,OAAO,OAAO;IAChC,IAAIA,OAAO,GAAG,EAAE,EAAE,OAAO,SAAS;IAClC,OAAO,WAAW;EACpB,CAAC;EAED,MAAMoC,WAAW,GAAG,CAClB;IACEC,KAAK,EAAE,gBAAgB;IACvBpB,MAAM,EAAExB,UAAU,CAACE,OAAO;IAC1B2C,IAAI,EAAEzD,QAAQ;IACd0D,WAAW,EAAE;EACf,CAAC,EACD;IACEF,KAAK,EAAE,UAAU;IACjBpB,MAAM,EAAExB,UAAU,CAACG,QAAQ;IAC3B0C,IAAI,EAAEtD,WAAW;IACjBuD,WAAW,EAAE;EACf,CAAC,EACD;IACEF,KAAK,EAAE,cAAc;IACrBpB,MAAM,EAAEkB,eAAe,CAAC1C,UAAU,CAACI,MAAM,CAACG,OAAO,CAAC;IAClDsC,IAAI,EAAEvD,KAAK;IACXwD,WAAW,EAAE,GAAG9C,UAAU,CAACI,MAAM,CAACG,OAAO,CAACwC,OAAO,CAAC,CAAC,CAAC,QAAQ;IAC5DC,KAAK,EAAE,GAAGhD,UAAU,CAACI,MAAM,CAACC,IAAI;EAClC,CAAC,EACD;IACEuC,KAAK,EAAE,WAAW;IAClBpB,MAAM,EAAExB,UAAU,CAACS,iBAAiB,GAAG,CAAC,GAAG,WAAW,GAAG,SAAS;IAClEoC,IAAI,EAAExD,MAAM;IACZyD,WAAW,EAAE,GAAG9C,UAAU,CAACS,iBAAiB;EAC9C,CAAC,CACF;EAED,IAAIC,OAAO,EAAE;IACX,oBACEb,OAAA;MAAKsC,SAAS,EAAC,0DAA0D;MAAAc,QAAA,gBACvEpD,OAAA;QAAIsC,SAAS,EAAC,0CAA0C;QAAAc,QAAA,EAAC;MAAa;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3E1C,OAAA;QAAKsC,SAAS,EAAC,WAAW;QAAAc,QAAA,EACvB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEC,CAAC,iBAClBtD,OAAA;UAAasC,SAAS,EAAC,2CAA2C;UAAAc,QAAA,gBAChEpD,OAAA;YAAKsC,SAAS,EAAC;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnD1C,OAAA;YAAKsC,SAAS,EAAC,QAAQ;YAAAc,QAAA,gBACrBpD,OAAA;cAAKsC,SAAS,EAAC;YAAoC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1D1C,OAAA;cAAKsC,SAAS,EAAC;YAA+B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC;QAAA,GALEY,CAAC;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMN,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE1C,OAAA;IAAKsC,SAAS,EAAC,0DAA0D;IAAAc,QAAA,gBACvEpD,OAAA;MAAIsC,SAAS,EAAC,0CAA0C;MAAAc,QAAA,EAAC;IAAa;MAAAb,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAE3E1C,OAAA;MAAKsC,SAAS,EAAC,WAAW;MAAAc,QAAA,EACvBN,WAAW,CAACO,GAAG,CAAC,CAACE,IAAI,EAAEC,KAAK,KAAK;QAChC,MAAMC,IAAI,GAAGF,IAAI,CAACP,IAAI;QACtB,oBACEhD,OAAA,CAACV,MAAM,CAACoE,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,KAAK,EAAER,KAAK,GAAG;UAAI,CAAE;UACnClB,SAAS,EAAC,+EAA+E;UAAAc,QAAA,gBAEzFpD,OAAA;YAAKsC,SAAS,EAAC,iEAAiE;YAAAc,QAAA,eAC9EpD,OAAA,CAACyD,IAAI;cAACnB,SAAS,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eAEN1C,OAAA;YAAKsC,SAAS,EAAC,QAAQ;YAAAc,QAAA,gBACrBpD,OAAA;cAAKsC,SAAS,EAAC,mCAAmC;cAAAc,QAAA,gBAChDpD,OAAA;gBAAIsC,SAAS,EAAC,2BAA2B;gBAAAc,QAAA,EAAEG,IAAI,CAACR;cAAK;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3D1C,OAAA;gBAAKsC,SAAS,EAAC,6BAA6B;gBAAAc,QAAA,GACzCf,aAAa,CAACkB,IAAI,CAAC5B,MAAM,CAAC,eAC3B3B,OAAA;kBAAMsC,SAAS,EAAE,uBAAuBK,cAAc,CAACY,IAAI,CAAC5B,MAAM,CAAC,EAAG;kBAAAyB,QAAA,EACnER,aAAa,CAACW,IAAI,CAAC5B,MAAM;gBAAC;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN1C,OAAA;cAAKsC,SAAS,EAAC,wCAAwC;cAAAc,QAAA,gBACrDpD,OAAA;gBAAGsC,SAAS,EAAC,uBAAuB;gBAAAc,QAAA,EAAEG,IAAI,CAACN;cAAW;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAC1Da,IAAI,CAACJ,KAAK,iBACTnD,OAAA;gBAAMsC,SAAS,EAAC,uBAAuB;gBAAAc,QAAA,EAAEG,IAAI,CAACJ;cAAK;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAC3D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GA1BDa,IAAI,CAACR,KAAK;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2BL,CAAC;MAEjB,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN1C,OAAA;MAAKsC,SAAS,EAAC,gCAAgC;MAAAc,QAAA,gBAC7CpD,OAAA;QAAKsC,SAAS,EAAC,wCAAwC;QAAAc,QAAA,gBACrDpD,OAAA;UAAMsC,SAAS,EAAC,mCAAmC;UAAAc,QAAA,EAAC;QAAY;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvE1C,OAAA;UAAMsC,SAAS,EAAC,uBAAuB;UAAAc,QAAA,GACpCjD,UAAU,CAACI,MAAM,CAACC,IAAI,EAAC,QAAM,EAACL,UAAU,CAACI,MAAM,CAACE,KAAK,EAAC,KACzD;QAAA;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1C,OAAA;QAAKsC,SAAS,EAAC,qCAAqC;QAAAc,QAAA,eAClDpD,OAAA,CAACV,MAAM,CAACoE,GAAG;UACTpB,SAAS,EAAE,oBACTnC,UAAU,CAACI,MAAM,CAACG,OAAO,GAAG,EAAE,GAC1B,YAAY,GACZP,UAAU,CAACI,MAAM,CAACG,OAAO,GAAG,EAAE,GAC9B,eAAe,GACf,cAAc,EACjB;UACHiD,OAAO,EAAE;YAAEM,KAAK,EAAE;UAAE,CAAE;UACtBH,OAAO,EAAE;YAAEG,KAAK,EAAE,GAAG9D,UAAU,CAACI,MAAM,CAACG,OAAO;UAAI,CAAE;UACpDqD,UAAU,EAAE;YAAEG,QAAQ,EAAE;UAAI;QAAE;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1C,OAAA;MAAKsC,SAAS,EAAC,6BAA6B;MAAAc,QAAA,gBAC1CpD,OAAA;QAAKsC,SAAS,EAAC,uCAAuC;QAAAc,QAAA,gBACpDpD,OAAA;UAAKsC,SAAS,EAAC,kCAAkC;UAAAc,QAAA,EAC9CjD,UAAU,CAACQ;QAAc;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACN1C,OAAA;UAAKsC,SAAS,EAAC,uBAAuB;UAAAc,QAAA,EAAC;QAAe;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,eACN1C,OAAA;QAAKsC,SAAS,EAAC,wCAAwC;QAAAc,QAAA,gBACrDpD,OAAA;UAAKsC,SAAS,EAAC,mCAAmC;UAAAc,QAAA,EAC/CjD,UAAU,CAACS;QAAiB;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACN1C,OAAA;UAAKsC,SAAS,EAAC,wBAAwB;UAAAc,QAAA,EAAC;QAAW;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxC,EAAA,CAjOID,YAAY;AAAAkE,EAAA,GAAZlE,YAAY;AAmOlB,eAAeA,YAAY;AAAC,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}